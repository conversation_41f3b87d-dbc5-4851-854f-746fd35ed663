# FireZone Admin Panel

A comprehensive Flutter-based admin panel for managing the "FireZone – Spin • Quiz • Win Diamonds" mobile application.

## 🔐 Admin Access

**Admin Email:** `<EMAIL>`

### How to Access Admin Panel

1. **From the App:**
   - Navigate to the admin access screen
   - Tap the FireZone logo 7 times consecutively
   - Sign in with the admin Google account

2. **Direct Access:**
   - Navigate to `/admin-login` route
   - Sign in with the admin Google account

## 🚀 Features

### 📊 Analytics Dashboard
- **User Statistics:** Total users, daily/weekly active users
- **Activity Metrics:** Total spins, quizzes completed
- **Redemption Stats:** Total and pending redemption requests
- **Real-time Data:** Auto-refreshing analytics with caching

### ❓ Quiz Management
- **CRUD Operations:** Add, edit, delete quiz questions
- **Categorization:** Organize by Weapons, Maps, Characters, Gameplay, General
- **Difficulty Levels:** Easy, Medium, Hard
- **Filtering:** Filter questions by category and difficulty
- **Validation:** Ensure proper question format with 4 options

### 💎 Redemption Management
- **Request Processing:** Approve or reject diamond redemption requests
- **User Information:** View user details, Free Fire UID, diamond count
- **Admin Notes:** Add notes when processing requests
- **Status Tracking:** Pending, approved, rejected status management

### 💡 Tips & Tricks Management
- **Content Management:** Add, edit, delete Free Fire tips
- **Categorization:** Organize tips by gameplay categories
- **Visibility Control:** Enable/disable tips for users
- **Rich Content:** Support for detailed tip descriptions

### 👥 User Management
- **User Overview:** View all registered users
- **Search Functionality:** Search users by email
- **User Statistics:** Points, streaks, activity metrics
- **Moderation:** Ban/unban users when necessary
- **Detailed Profiles:** Complete user information and activity history

## 🛠️ Technical Implementation

### Architecture
```
lib/
├── models/
│   └── admin_models.dart          # Admin-specific data models
├── services/
│   └── admin_service.dart         # Firebase operations for admin
├── providers/
│   └── admin_provider.dart        # State management for admin features
├── screens/admin/
│   ├── admin_login_screen.dart    # Admin authentication
│   ├── admin_dashboard_screen.dart # Main admin dashboard
│   ├── admin_analytics_screen.dart # Analytics and statistics
│   ├── admin_quiz_screen.dart     # Quiz management
│   ├── admin_redemptions_screen.dart # Redemption processing
│   ├── admin_tips_screen.dart     # Tips management
│   └── admin_users_screen.dart    # User management
└── screens/
    └── admin_access_screen.dart   # Secret access point
```

### Security Features
- **Email Restriction:** Only `<EMAIL>` can access
- **Firebase Rules:** Server-side validation of admin permissions
- **Authentication Required:** Google Sign-In mandatory
- **Session Management:** Automatic logout on unauthorized access

### Firebase Integration
- **Firestore Collections:**
  - `users` - User data and statistics
  - `quiz` - Quiz questions and answers
  - `redemptions` - Diamond redemption requests
  - `tips` - Free Fire tips and guides
  - `analytics` - Cached analytics data

- **Realtime Database:**
  - `leaderboard` - Daily and weekly leaderboards

## 📱 User Interface

### Design Principles
- **Dark Theme:** Consistent with main app design
- **Responsive Layout:** Adapts to different screen sizes
- **Intuitive Navigation:** Clear section organization
- **Loading States:** Progress indicators for all operations
- **Error Handling:** User-friendly error messages

### Key Components
- **Dashboard Cards:** Quick access to main functions
- **Data Tables:** Organized display of information
- **Action Dialogs:** Confirmation for critical operations
- **Filter Controls:** Easy data filtering and searching
- **Status Indicators:** Visual status representation

## 🔧 Setup Instructions

### Prerequisites
- Flutter SDK (latest stable version)
- Firebase project with Firestore and Authentication enabled
- Google Sign-In configured for admin email

### Installation
1. **Clone the repository**
2. **Install dependencies:**
   ```bash
   flutter pub get
   ```
3. **Configure Firebase:**
   - Add `google-services.json` (Android)
   - Add `GoogleService-Info.plist` (iOS)
   - Update Firebase configuration

4. **Update Admin Email:**
   ```dart
   // lib/utils/constants.dart
   static const String adminEmail = '<EMAIL>';
   ```

5. **Run the application:**
   ```bash
   flutter run
   ```

## 🔒 Firebase Security Rules

### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin-only collections
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    match /quiz/{questionId} {
      allow read, write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    match /redemptions/{requestId} {
      allow read, write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    match /tips/{tipId} {
      allow read, write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    match /analytics/{docId} {
      allow read, write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
  }
}
```

### Realtime Database Rules
```json
{
  "rules": {
    "leaderboard": {
      ".read": "auth != null && auth.token.email == '<EMAIL>'",
      ".write": "auth != null && auth.token.email == '<EMAIL>'"
    }
  }
}
```

## 📊 Data Models

### Quiz Question
```dart
class AdminQuizQuestion {
  final String id;
  final String question;
  final List<String> options;      // 4 options (A, B, C, D)
  final String correctAnswer;
  final String category;           // Weapons, Maps, etc.
  final String difficulty;        // Easy, Medium, Hard
  final DateTime createdAt;
  final DateTime? updatedAt;
}
```

### Redemption Request
```dart
class AdminRedemptionRequest {
  final String id;
  final String userId;
  final String userEmail;
  final String userName;
  final int diamondCount;
  final String freeFireUID;
  final String status;             // pending, approved, rejected
  final DateTime requestDate;
  final DateTime? processedDate;
  final String? adminNotes;
}
```

## 🚨 Important Notes

### Security Considerations
- **Never share admin credentials**
- **Regularly monitor admin access logs**
- **Use strong authentication methods**
- **Keep Firebase rules updated**

### Data Management
- **Regular backups recommended**
- **Monitor Firestore usage and costs**
- **Implement data retention policies**
- **Cache analytics data to reduce reads**

### Maintenance
- **Regular updates to Flutter and dependencies**
- **Monitor app performance and errors**
- **Review and update admin permissions**
- **Test all admin functions regularly**

## 📞 Support

For technical support or questions about the admin panel:
- **Email:** <EMAIL>
- **Documentation:** This README file
- **Firebase Console:** Monitor real-time data and errors

## ⚠️ Disclaimer

This admin panel is specifically designed for managing the FireZone application and is not affiliated with Garena or Free Fire. All game-related content is for educational and entertainment purposes only.
