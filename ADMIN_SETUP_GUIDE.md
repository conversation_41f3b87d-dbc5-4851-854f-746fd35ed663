# FireZone Admin Panel Setup Guide

This guide will help you set up and deploy the FireZone Admin Panel for managing your app.

## 📋 Prerequisites

### Required Software
- **Flutter SDK** (3.0.0 or higher)
- **Android Studio** or **VS Code** with Flutter extensions
- **Firebase CLI** (for deployment)
- **Git** (for version control)

### Firebase Project Requirements
- **Firebase Authentication** enabled with Google Sign-In
- **Cloud Firestore** database
- **Firebase Realtime Database**
- **Firebase Hosting** (optional, for web deployment)

## 🚀 Step-by-Step Setup

### 1. Firebase Project Configuration

#### 1.1 Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `firezone-admin` (or your preferred name)
4. Enable Google Analytics (optional)
5. Create project

#### 1.2 Enable Authentication
1. In Firebase Console, go to **Authentication**
2. Click **Get started**
3. Go to **Sign-in method** tab
4. Enable **Google** sign-in provider
5. Add your admin email: `<EMAIL>`
6. Save configuration

#### 1.3 Create Firestore Database
1. Go to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (we'll update rules later)
4. Select your preferred location
5. Create database

#### 1.4 Create Realtime Database
1. Go to **Realtime Database**
2. Click **Create Database**
3. Choose **Start in locked mode**
4. Select your preferred location
5. Create database

### 2. Flutter Project Setup

#### 2.1 Clone and Configure
```bash
# Clone the project (if not already done)
git clone <your-repo-url>
cd firezone

# Install dependencies
flutter pub get
```

#### 2.2 Firebase Configuration
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Install FlutterFire CLI
dart pub global activate flutterfire_cli

# Configure Firebase for Flutter
flutterfire configure
```

#### 2.3 Update Admin Email
Edit `lib/utils/constants.dart`:
```dart
// Update this line with your admin email
static const String adminEmail = '<EMAIL>';
```

### 3. Firebase Security Rules Deployment

#### 3.1 Deploy Firestore Rules
```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules
```

#### 3.2 Deploy Realtime Database Rules
```bash
# Deploy Realtime Database rules
firebase deploy --only database
```

### 4. Testing the Admin Panel

#### 4.1 Run the App
```bash
# Run on Android emulator/device
flutter run

# Or run on iOS simulator/device
flutter run -d ios
```

#### 4.2 Access Admin Panel
1. **Method 1: Secret Access**
   - Open the app
   - Navigate to the admin access screen
   - Tap the FireZone logo 7 times
   - Sign in with admin Google account

2. **Method 2: Direct Navigation**
   - Add navigation to admin login in your app
   - Or use deep linking to `/admin-login`

#### 4.3 Test Admin Functions
1. **Analytics Dashboard**
   - Verify user statistics display
   - Check data refresh functionality

2. **Quiz Management**
   - Add a test quiz question
   - Edit and delete questions
   - Test category filtering

3. **User Management**
   - Search for users
   - View user details
   - Test ban/unban functionality

4. **Redemption Management**
   - Create test redemption requests
   - Approve/reject requests
   - Add admin notes

## 🔧 Advanced Configuration

### Custom Domain Setup (Optional)
If you want to host the admin panel on the web:

```bash
# Initialize Firebase Hosting
firebase init hosting

# Build Flutter web
flutter build web

# Deploy to Firebase Hosting
firebase deploy --only hosting
```

### Environment Variables
Create `.env` file for different environments:
```env
# Development
FIREBASE_PROJECT_ID=firezone-dev
ADMIN_EMAIL=<EMAIL>

# Production
FIREBASE_PROJECT_ID=firezone-prod
ADMIN_EMAIL=<EMAIL>
```

### Analytics Configuration
Set up additional analytics tracking:

```dart
// lib/services/analytics_service.dart
class AnalyticsService {
  static Future<void> logAdminAction(String action, Map<String, dynamic> parameters) async {
    // Log admin actions for monitoring
    await FirebaseAnalytics.instance.logEvent(
      name: 'admin_action',
      parameters: {
        'action': action,
        'admin_email': AppConstants.adminEmail,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        ...parameters,
      },
    );
  }
}
```

## 🔒 Security Best Practices

### 1. Admin Email Security
- Use a dedicated admin email account
- Enable 2FA on the admin Google account
- Regularly review admin access logs

### 2. Firebase Security
- Regularly update Firebase Security Rules
- Monitor Firebase usage and billing
- Set up Firebase Security Rules testing

### 3. App Security
- Implement proper error handling
- Add logging for admin actions
- Regular security audits

### 4. Data Protection
- Regular database backups
- Implement data retention policies
- Monitor for suspicious activities

## 📊 Monitoring and Maintenance

### Firebase Console Monitoring
1. **Authentication Usage**
   - Monitor sign-in attempts
   - Check for unauthorized access

2. **Database Usage**
   - Monitor read/write operations
   - Check for unusual data patterns

3. **Performance Monitoring**
   - Track app performance
   - Monitor crash reports

### Regular Maintenance Tasks
- **Weekly**: Review admin action logs
- **Monthly**: Update dependencies and security rules
- **Quarterly**: Security audit and backup verification

## 🚨 Troubleshooting

### Common Issues

#### 1. Authentication Errors
```
Error: Admin access denied
```
**Solution**: Verify admin email in constants and Firebase rules

#### 2. Firestore Permission Errors
```
Error: Missing or insufficient permissions
```
**Solution**: Check and redeploy Firestore security rules

#### 3. Build Errors
```
Error: Firebase configuration not found
```
**Solution**: Run `flutterfire configure` again

### Debug Mode
Enable debug logging:
```dart
// lib/main.dart
void main() async {
  // Enable debug logging
  if (kDebugMode) {
    FirebaseFirestore.instance.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
  }
  
  runApp(const FireZoneApp());
}
```

## 📞 Support

### Getting Help
- **Documentation**: Refer to this guide and README files
- **Firebase Support**: [Firebase Support](https://firebase.google.com/support)
- **Flutter Support**: [Flutter Documentation](https://flutter.dev/docs)

### Reporting Issues
When reporting issues, include:
- Flutter version (`flutter --version`)
- Firebase project ID
- Error messages and stack traces
- Steps to reproduce the issue

## 🎯 Next Steps

After successful setup:
1. **Customize the admin panel** for your specific needs
2. **Add additional admin features** as required
3. **Set up monitoring and alerts**
4. **Train admin users** on the panel usage
5. **Implement backup and recovery procedures**

## ✅ Deployment Checklist

Before going live:
- [ ] Admin email configured correctly
- [ ] Firebase security rules deployed
- [ ] All admin functions tested
- [ ] Monitoring and logging set up
- [ ] Backup procedures in place
- [ ] Admin user training completed
- [ ] Security audit performed
- [ ] Performance testing done

---

**Important**: Always test the admin panel thoroughly in a development environment before deploying to production!
