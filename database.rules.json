{"rules": {".read": false, ".write": false, "leaderboard": {"daily": {".read": "auth != null", "$uid": {".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.uid == $uid)", ".validate": "newData.hasChildren(['name', 'points', 'avatar', 'timestamp'])"}}, "weekly": {".read": "auth != null", "$uid": {".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.uid == $uid)", ".validate": "newData.hasChildren(['name', 'points', 'avatar', 'timestamp'])"}}, "monthly": {".read": "auth != null", "$uid": {".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.uid == $uid)", ".validate": "newData.hasChildren(['name', 'points', 'avatar', 'timestamp'])"}}, "allTime": {".read": "auth != null", "$uid": {".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.uid == $uid)", ".validate": "newData.hasChildren(['name', 'points', 'avatar', 'timestamp'])"}}}, "userStats": {"$uid": {".read": "auth != null && (auth.uid == $uid || auth.token.email == '<EMAIL>')", ".write": "auth != null && (auth.uid == $uid || auth.token.email == '<EMAIL>')", "totalSpins": {".validate": "newData.isNumber() && newData.val() >= 0"}, "totalQuizzes": {".validate": "newData.isNumber() && newData.val() >= 0"}, "totalPoints": {".validate": "newData.isNumber() && newData.val() >= 0"}, "lastActive": {".validate": "newData.isNumber()"}}}, "gameStats": {".read": "auth != null && auth.token.email == '<EMAIL>'", ".write": "auth != null && auth.token.email == '<EMAIL>'", "totalUsers": {".validate": "newData.isNumber() && newData.val() >= 0"}, "dailyActiveUsers": {".validate": "newData.isNumber() && newData.val() >= 0"}, "weeklyActiveUsers": {".validate": "newData.isNumber() && newData.val() >= 0"}, "totalSpins": {".validate": "newData.isNumber() && newData.val() >= 0"}, "totalQuizzes": {".validate": "newData.isNumber() && newData.val() >= 0"}, "totalRedemptions": {".validate": "newData.isNumber() && newData.val() >= 0"}, "lastUpdated": {".validate": "newData.isNumber()"}}, "adminLogs": {".read": "auth != null && auth.token.email == '<EMAIL>'", ".write": "auth != null && auth.token.email == '<EMAIL>'", "$logId": {"action": {".validate": "newData.isString() && newData.val().length > 0"}, "timestamp": {".validate": "newData.isNumber()"}, "adminEmail": {".validate": "newData.isString() && newData.val() == '<EMAIL>'"}, "details": {".validate": "newData.isString()"}}}}}