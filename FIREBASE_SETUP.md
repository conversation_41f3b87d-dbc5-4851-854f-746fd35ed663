# Firebase Setup Guide for FireZone App

## Issues Fixed
1. ✅ Android NDK version updated to 27.0.12077973
2. ✅ Added required permissions to AndroidManifest.xml
3. ✅ Added AdMob App ID to AndroidManifest.xml
4. ✅ Added Google Services plugin configuration
5. ✅ Added error handling to main.dart
6. ✅ Created Firestore security rules

## Remaining Setup Required

### 1. Firebase Project Configuration

You need to configure your Firebase project with real values. The current `lib/firebase_options.dart` contains placeholder values.

**Steps:**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project or create a new one
3. Go to Project Settings > General
4. Add Android app with package name: `com.anuved.firezone`
5. Download the `google-services.json` file and replace the existing one in `android/app/`
6. Run `flutter packages pub run build_runner build` to regenerate firebase_options.dart

### 2. Firestore Security Rules

Upload the `firestore.rules` file to your Firebase project:

1. Go to Firebase Console > Firestore Database > Rules
2. Copy the content from `firestore.rules` and paste it
3. Click "Publish"

### 3. Google Sign-In Configuration

**For Android:**
1. Go to Firebase Console > Authentication > Sign-in method
2. Enable Google sign-in
3. Get your SHA-1 fingerprint:
   ```bash
   cd android
   ./gradlew signingReport
   ```
4. Add the SHA-1 fingerprint to your Firebase project:
   - Go to Project Settings > General > Your apps
   - Click on Android app
   - Add fingerprint

**For Debug builds, use:**
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

### 4. AdMob Configuration

The app currently uses test AdMob IDs. For production:

1. Create an AdMob account
2. Create an app in AdMob console
3. Create ad units (Banner, Interstitial, Rewarded)
4. Replace test IDs in:
   - `android/app/src/main/AndroidManifest.xml` (App ID)
   - `lib/utils/constants.dart` (Ad Unit IDs)

### 5. Firebase Authentication Setup

1. Go to Firebase Console > Authentication
2. Enable Email/Password sign-in
3. Enable Google sign-in
4. Configure authorized domains if needed

### 6. Firestore Database Setup

1. Go to Firebase Console > Firestore Database
2. Create database in production mode
3. Apply the security rules from `firestore.rules`

### 7. Realtime Database Setup (for Leaderboard)

1. Go to Firebase Console > Realtime Database
2. Create database
3. Set up rules for leaderboard access

## Testing the Fixes

After completing the Firebase setup:

1. Clean and rebuild:
   ```bash
   flutter clean
   flutter pub get
   flutter run
   ```

2. Test authentication:
   - Try email signup/login
   - Try Google sign-in
   - Check if user profile is created in Firestore

3. Check logs for any remaining errors

## Common Issues

1. **Google Sign-In fails**: Check SHA-1 fingerprint configuration
2. **Firestore permission denied**: Verify security rules are applied
3. **AdMob errors**: Ensure App ID is correctly configured
4. **Build errors**: Clean project and rebuild

## Production Checklist

- [ ] Replace firebase_options.dart with real values
- [ ] Upload Firestore security rules
- [ ] Configure Google Sign-In with SHA-1 fingerprints
- [ ] Replace AdMob test IDs with real ones
- [ ] Test all authentication flows
- [ ] Test Firestore read/write operations
- [ ] Test AdMob integration
