const functions = require('firebase-functions');
const admin = require('firebase-admin');
const axios = require('axios');

admin.initializeApp();

// OpenAI API configuration - Get from environment
const getOpenAIKey = () => {
  return process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
};
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

// Scheduled function to generate daily quiz (runs at 00:00 UTC daily)
exports.generateDailyQuiz = functions.pubsub.schedule('0 0 * * *')
  .timeZone('UTC')
  .onRun(async (context) => {
    try {
      console.log('Starting daily quiz generation...');

      const today = new Date();
      const dateString = today.toISOString().split('T')[0]; // YYYY-MM-DD format

      // Check if quiz already exists for today
      const existingQuiz = await admin.firestore()
        .collection('daily_quizzes')
        .doc(dateString)
        .get();

      if (existingQuiz.exists) {
        console.log(`Quiz already exists for ${dateString}`);
        return null;
      }

      // Generate 5 quiz questions using OpenAI
      const questions = await generateQuizQuestions();

      // Store in Firestore
      await admin.firestore()
        .collection('daily_quizzes')
        .doc(dateString)
        .set({
          date: dateString,
          questions: questions,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          totalQuestions: questions.length
        });

      console.log(`Successfully generated quiz for ${dateString}`);
      return null;
    } catch (error) {
      console.error('Error generating daily quiz:', error);
      throw error;
    }
  });

// Function to generate quiz questions using OpenAI
async function generateQuizQuestions() {
  const prompt = `Generate 15 multiple-choice quiz questions about the mobile game Free Fire. Each question should be about game mechanics, characters, weapons, maps, or strategies.

Format your response as a JSON array with exactly this structure:
[
  {
    "question": "What is the maximum number of players in a Free Fire match?",
    "options": ["40", "50", "60", "70"],
    "correctAnswer": 2,
    "explanation": "Free Fire matches can have up to 50 players maximum."
  }
]

Make sure:
- Each question has exactly 4 options
- correctAnswer is the index (0-3) of the correct option
- Questions are varied and cover different aspects of Free Fire
- Include brief explanations for educational value
- Questions should be challenging but fair for Free Fire players
- Generate exactly 15 questions for a comprehensive trivia experience`;

  try {
    const response = await axios.post(OPENAI_API_URL, {
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a Free Fire game expert who creates educational quiz questions. Always respond with valid JSON only.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${getOpenAIKey()}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0].message.content;
    console.log('AI Response:', aiResponse);
    
    // Parse the JSON response
    let questions;
    try {
      questions = JSON.parse(aiResponse);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      // Fallback to manual parsing if needed
      questions = parseQuestionsManually(aiResponse);
    }
    
    // Validate and clean the questions
    const validatedQuestions = validateQuestions(questions);
    
    if (validatedQuestions.length !== 15) {
      throw new Error(`Expected 15 questions, got ${validatedQuestions.length}`);
    }
    
    return validatedQuestions;
  } catch (error) {
    console.error('Error calling OpenAI API:', error);
    
    // Fallback questions if API fails
    return getFallbackQuestions();
  }
}

// Validate and clean questions
function validateQuestions(questions) {
  if (!Array.isArray(questions)) {
    throw new Error('Questions must be an array');
  }
  
  return questions.map((q, index) => {
    if (!q.question || !Array.isArray(q.options) || q.options.length !== 4) {
      throw new Error(`Invalid question format at index ${index}`);
    }
    
    const correctAnswer = parseInt(q.correctAnswer);
    if (isNaN(correctAnswer) || correctAnswer < 0 || correctAnswer > 3) {
      throw new Error(`Invalid correct answer at index ${index}`);
    }
    
    return {
      id: `q${index + 1}`,
      question: q.question.trim(),
      options: q.options.map(opt => opt.trim()),
      correctAnswer: correctAnswer,
      explanation: q.explanation || 'No explanation provided.',
      points: 20 // Each question worth 20 points
    };
  });
}

// Manual parsing fallback
function parseQuestionsManually(response) {
  // Try to extract JSON from response if it's wrapped in text
  const jsonMatch = response.match(/\[[\s\S]*\]/);
  if (jsonMatch) {
    try {
      return JSON.parse(jsonMatch[0]);
    } catch (e) {
      console.error('Manual parsing failed:', e);
    }
  }
  
  // If all else fails, return fallback
  return getFallbackQuestions();
}

// Fallback questions if AI API fails
function getFallbackQuestions() {
  return [
    {
      id: 'q1',
      question: 'What is the maximum number of players in a Free Fire match?',
      options: ['40 players', '50 players', '60 players', '70 players'],
      correctAnswer: 1,
      explanation: 'Free Fire matches can have up to 50 players maximum.',
      points: 20
    },
    {
      id: 'q2',
      question: 'Which character has the ability to create gloo walls?',
      options: ['Kelly', 'Chrono', 'Alok', 'DJ Alok'],
      correctAnswer: 1,
      explanation: 'Chrono has the Time Turner ability that creates protective force fields.',
      points: 20
    },
    {
      id: 'q3',
      question: 'What is the name of the main island map in Free Fire?',
      options: ['Bermuda', 'Purgatory', 'Kalahari', 'Alpine'],
      correctAnswer: 0,
      explanation: 'Bermuda is the classic and most popular map in Free Fire.',
      points: 20
    },
    {
      id: 'q4',
      question: 'Which weapon type has the highest damage per shot?',
      options: ['Assault Rifle', 'Sniper Rifle', 'SMG', 'Shotgun'],
      correctAnswer: 1,
      explanation: 'Sniper rifles like AWM deal the highest damage per shot.',
      points: 20
    },
    {
      id: 'q5',
      question: 'What does the term "Booyah" mean in Free Fire?',
      options: ['Getting a kill', 'Winning the match', 'Finding rare loot', 'Reviving teammate'],
      correctAnswer: 1,
      explanation: 'Booyah is displayed when you win a match in Free Fire.',
      points: 20
    },
    {
      id: 'q6',
      question: 'Which character has the fastest movement speed?',
      options: ['Kelly', 'Andrew', 'Moco', 'Paloma'],
      correctAnswer: 0,
      explanation: 'Kelly has the Dash ability that increases sprinting speed.',
      points: 20
    },
    {
      id: 'q7',
      question: 'What is the rarest item in Free Fire?',
      options: ['AWM', 'Level 3 Helmet', 'Airdrop', 'Gloo Wall'],
      correctAnswer: 0,
      explanation: 'AWM sniper rifle is the rarest and most powerful weapon.',
      points: 20
    },
    {
      id: 'q8',
      question: 'Which pet provides healing abilities?',
      options: ['Robo', 'Spirit Fox', 'Ottero', 'Detective Panda'],
      correctAnswer: 2,
      explanation: 'Ottero can recover HP when using healing items.',
      points: 20
    },
    {
      id: 'q9',
      question: 'What is the safe zone called in Free Fire?',
      options: ['Play Zone', 'Safe Area', 'Blue Zone', 'White Circle'],
      correctAnswer: 0,
      explanation: 'The safe area is called the Play Zone in Free Fire.',
      points: 20
    },
    {
      id: 'q10',
      question: 'Which weapon has the fastest fire rate?',
      options: ['AK47', 'MP40', 'Vector', 'M1014'],
      correctAnswer: 2,
      explanation: 'Vector has the highest fire rate among all weapons.',
      points: 20
    },
    {
      id: 'q11',
      question: 'What does the character Wukong transform into?',
      options: ['Rock', 'Bush', 'Tree', 'Wall'],
      correctAnswer: 1,
      explanation: 'Wukong can camouflage by transforming into a bush.',
      points: 20
    },
    {
      id: 'q12',
      question: 'Which mode allows 4 players per team?',
      options: ['Solo', 'Duo', 'Squad', 'Clash Squad'],
      correctAnswer: 2,
      explanation: 'Squad mode allows teams of up to 4 players.',
      points: 20
    },
    {
      id: 'q13',
      question: 'What is the currency used to buy characters?',
      options: ['Gold', 'Diamonds', 'Coins', 'All of the above'],
      correctAnswer: 3,
      explanation: 'Characters can be purchased with Gold, Diamonds, or Coins.',
      points: 20
    },
    {
      id: 'q14',
      question: 'Which vehicle is the fastest in Free Fire?',
      options: ['Motorcycle', 'Car', 'Jeep', 'Monster Truck'],
      correctAnswer: 0,
      explanation: 'Motorcycle is the fastest vehicle in Free Fire.',
      points: 20
    },
    {
      id: 'q15',
      question: 'What happens when you get a "Headshot"?',
      options: ['Extra damage', 'Instant kill', 'Bonus points', 'All of the above'],
      correctAnswer: 0,
      explanation: 'Headshots deal extra damage to enemies.',
      points: 20
    }
  ];
}

// HTTP function to manually trigger quiz generation (for testing)
exports.generateQuizManual = functions.https.onRequest(async (req, res) => {
  try {
    const questions = await generateQuizQuestions();
    const today = new Date().toISOString().split('T')[0];

    await admin.firestore()
      .collection('daily_quizzes')
      .doc(today)
      .set({
        date: today,
        questions: questions,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        totalQuestions: questions.length
      });

    res.json({ success: true, questions: questions });
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ error: error.message });
  }
});
