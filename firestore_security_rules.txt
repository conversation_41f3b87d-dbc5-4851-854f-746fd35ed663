// Firestore Security Rules for FireZone Redemption System
// Copy these rules to your Firebase Console > Firestore Database > Rules

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - existing rules enhanced
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        (request.auth.token.email == '<EMAIL>' || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Redemption requests collection - NEW
    match /redemption_requests/{requestId} {
      // Users can read their own requests
      allow read: if request.auth != null && 
        (resource.data.userId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
      
      // Users can create their own requests
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.userId &&
        validateRedemptionRequest();
      
      // Users can update their own pending requests (for cancellation)
      allow update: if request.auth != null && 
        (resource.data.userId == request.auth.uid && 
         resource.data.status == 'pending' &&
         request.resource.data.status == 'cancelled') ||
        // Admins can update any request
        (request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
      
      // Only admins can delete
      allow delete: if request.auth != null && 
        (request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Rewards collection - existing
    match /rewards/{rewardId} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Leaderboard collection - existing
    match /leaderboard/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == userId ||
         request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Admin logs collection - NEW
    match /admin_logs/{logId} {
      allow read, write: if request.auth != null && 
        (request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // App settings collection - NEW
    match /app_settings/{settingId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.token.email == '<EMAIL>' ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Validation functions
    function validateRedemptionRequest() {
      return request.resource.data.keys().hasAll(['userId', 'userName', 'userEmail', 'type', 'pointsUsed', 'amount', 'status', 'requestDate']) &&
             request.resource.data.userId == request.auth.uid &&
             request.resource.data.status == 'pending' &&
             request.resource.data.pointsUsed is int &&
             request.resource.data.pointsUsed > 0 &&
             request.resource.data.amount is number &&
             request.resource.data.amount > 0 &&
             request.resource.data.type in ['rupees', 'diamonds'];
    }
  }
}
