# FireZone Redemption & Withdrawal System

## Overview
A comprehensive redemption and withdrawal system that allows users to convert their earned points into real money (INR) or Free Fire diamonds. The system includes user payment settings, withdrawal requests, admin management, and complete transaction tracking.

## Features

### 🎯 User Features
- **Payment Settings**: Users can set up UPI ID, Free Fire Game ID, and phone number
- **Dual Withdrawal Options**: 
  - Convert points to Indian Rupees (INR) via UPI
  - Convert points to Free Fire Diamonds via Game ID
- **Withdrawal History**: Complete transaction history with status tracking
- **Request Management**: Users can cancel pending withdrawal requests
- **Real-time Conversion**: Live conversion rates display (100 points = ₹1, 50 points = 1 diamond)

### 🛡️ Admin Features
- **Comprehensive Dashboard**: View all withdrawal requests with filtering and search
- **Request Management**: Approve, reject, or complete withdrawal requests
- **Status Tracking**: Track requests through pending → approved → completed workflow
- **Transaction Records**: Add transaction IDs and admin notes
- **User Management**: Full access to user payment details and history

### 🔒 Security Features
- **Admin Access Control**: Only <EMAIL> has full admin privileges
- **Request Validation**: Verify user points before processing withdrawals
- **Payment Verification**: Validate UPI IDs and Game IDs before allowing withdrawals
- **Audit Trail**: Complete logging of all admin actions and status changes

## System Architecture

### Models
- **RedemptionRequest**: Core withdrawal request model with all transaction details
- **UserPaymentProfile**: User payment information storage
- **RedemptionType**: Enum for rupees vs diamonds
- **RedemptionStatus**: Enum for request lifecycle (pending, approved, rejected, completed, cancelled)

### Services
- **RedemptionService**: Handles all withdrawal operations and database interactions
- **FirebaseService**: Enhanced with user profile updates for payment details

### Screens
- **PaymentSettingsScreen**: User setup for UPI ID, Game ID, and phone number
- **EnhancedWalletScreen**: Comprehensive wallet with withdrawal options
- **WithdrawalScreen**: Detailed withdrawal request form
- **WithdrawalHistoryScreen**: User's transaction history
- **RedemptionManagementScreen**: Admin panel for managing all requests

## Conversion Rates
- **Rupees**: 100 points = ₹1 INR (minimum withdrawal: 100 points)
- **Diamonds**: 50 points = 1 Free Fire Diamond (minimum withdrawal: 50 points)

## User Workflow

### 1. Setup Payment Details
```
Profile → Payment Settings → Enter UPI ID/Game ID → Save
```

### 2. Make Withdrawal Request
```
Wallet → Withdraw Rupees/Diamonds → Enter Points → Submit Request
```

### 3. Track Request Status
```
Profile → Withdrawal History → View Status & Details
```

### 4. Cancel Pending Requests (Optional)
```
Withdrawal History → Select Pending Request → Cancel (Points Refunded)
```

## Admin Workflow

### 1. View Requests
```
Admin Panel → Redemption Management → View All/Pending/Completed
```

### 2. Process Requests
```
Select Request → Approve/Reject/Complete → Add Notes → Confirm
```

### 3. Track Payments
```
Complete Request → Add Transaction ID → Mark as Completed
```

## Database Structure

### Collections
- `redemption_requests`: All withdrawal requests
- `users`: Enhanced with payment details (upiId, gameId, phoneNumber)

### Request Document Structure
```json
{
  "id": "auto-generated",
  "userId": "user-uid",
  "userName": "User Name",
  "userEmail": "<EMAIL>",
  "type": "rupees|diamonds",
  "pointsUsed": 100,
  "amount": 1.0,
  "status": "pending|approved|rejected|completed|cancelled",
  "requestDate": "timestamp",
  "processedDate": "timestamp",
  "completedDate": "timestamp",
  "processedBy": "admin-email",
  "adminNotes": "Admin notes",
  "upiId": "user-upi-id",
  "gameId": "user-game-id",
  "phoneNumber": "user-phone",
  "transactionId": "payment-transaction-id"
}
```

## Validation Rules

### UPI ID Validation
- Must contain '@' symbol
- Format: `username@provider` (e.g., `**********@paytm`)

### Game ID Validation
- Must be 9-12 digits
- Numeric only
- Free Fire Game ID format

### Phone Number Validation
- Indian phone numbers only
- Format: `+91XXXXXXXXXX` or `XXXXXXXXXX`
- Must start with 6-9

### Points Validation
- Minimum withdrawal: 100 points for rupees, 50 points for diamonds
- Cannot exceed user's current point balance
- Points are deducted immediately upon request submission

## Error Handling
- Comprehensive error messages for validation failures
- Graceful handling of network errors
- User-friendly error displays
- Admin error logging and notifications

## Future Enhancements
- Multiple payment methods (bank transfer, PayPal, etc.)
- Bulk payment processing for admins
- Automated payment integration
- Advanced analytics and reporting
- Email/SMS notifications for status updates
- Withdrawal limits and daily caps
- KYC verification for large withdrawals

## Security Considerations
- All admin actions are logged with timestamps and user identification
- Payment details are encrypted and securely stored
- Request validation prevents duplicate submissions
- Admin access is restricted to verified email addresses
- Points are immediately deducted to prevent double-spending

## Testing Checklist
- [ ] User can set up payment details
- [ ] Withdrawal requests are created correctly
- [ ] Points are deducted immediately
- [ ] Admin can view and process requests
- [ ] Status updates work correctly
- [ ] Transaction IDs are stored properly
- [ ] Users can cancel pending requests
- [ ] Points are refunded on cancellation
- [ ] Validation works for all input fields
- [ ] Error handling works correctly

## Support Information
For user support regarding withdrawals:
- Email: <EMAIL>
- Processing time: 24-48 hours for rupees, 1-2 hours for diamonds
- UPI payments are usually instant once processed
- Diamond transfers require manual processing

---

**Note**: This system is designed to handle real money transactions. Ensure proper testing in a sandbox environment before deploying to production. Consider implementing additional security measures and compliance requirements based on your jurisdiction's financial regulations.
