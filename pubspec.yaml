name: firezone
description: "FireZone – Spin • Quiz • Win Diamonds - A Free Fire rewards app"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  lottie: ^3.0.0

  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_database: ^10.3.8
  google_sign_in: ^6.1.6

  # AdMob
  google_mobile_ads: ^4.0.0

  # State Management
  provider: ^6.1.1

  # Storage & Utilities
  shared_preferences: ^2.2.2
  uuid: ^4.2.1
  intl: ^0.19.0

  # Animations & UI
  flutter_staggered_animations: ^1.1.1
  flutter_spinkit: ^5.2.0

  # Networking
  http: ^1.1.2

  # Share functionality
  share_plus: ^7.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/data/
