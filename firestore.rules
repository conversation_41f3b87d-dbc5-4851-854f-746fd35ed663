rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Quiz collection - authenticated users can read
    match /quiz/{document} {
      allow read: if request.auth != null;
    }
    
    // Rewards collection - users can read/write their own rewards
    match /rewards/{document} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Redemptions collection - users can read/write their own redemptions
    match /redemptions/{document} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Quiz sessions - users can read/write their own sessions
    match /quiz_sessions/{document} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Leaderboard collection - authenticated users can read
    match /leaderboard/{document} {
      allow read: if request.auth != null;
    }
  }
}
