rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && request.auth.token.email == '<EMAIL>';
    }

    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user owns the document
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    // Users collection - Admin can read/write all, users can read/write their own
    match /users/{userId} {
      allow read, write: if isAdmin();
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() && request.auth.uid == userId;
    }

    // Quiz collection - Admin can manage, users can only read
    match /quiz/{questionId} {
      allow read, write: if isAdmin();
      allow read: if isAuthenticated();
    }

    // Rewards collection - Admin can manage, users can read/write their own
    match /rewards/{document} {
      allow read, write: if isAdmin();
      allow read, write: if isAuthenticated() &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // Redemptions collection - Admin can manage all, users can create/read their own
    match /redemptions/{requestId} {
      allow read, write: if isAdmin();
      allow create: if isAuthenticated();
      allow read: if isAuthenticated() &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // Tips collection - Admin can manage, users can only read active tips
    match /tips/{tipId} {
      allow read, write: if isAdmin();
      allow read: if isAuthenticated();
    }

    // Analytics collection - Admin only
    match /analytics/{docId} {
      allow read, write: if isAdmin();
    }

    // Notifications collection - Admin only
    match /notifications/{notificationId} {
      allow read, write: if isAdmin();
    }

    // Quiz sessions - users can read/write their own sessions
    match /quiz_sessions/{document} {
      allow read, write: if isAuthenticated() &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // Leaderboard collection - Admin can manage, users can read
    match /leaderboard/{document} {
      allow read, write: if isAdmin();
      allow read: if isAuthenticated();
    }
  }
}
