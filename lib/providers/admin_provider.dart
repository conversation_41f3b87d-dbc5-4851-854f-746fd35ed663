import 'package:flutter/foundation.dart';
import '../models/admin_models.dart';
import '../services/admin_service.dart';

enum AdminLoadingState { idle, loading, success, error }

class AdminProvider extends ChangeNotifier {
  AdminLoadingState _state = AdminLoadingState.idle;
  String? _errorMessage;

  // Analytics
  AdminAnalytics? _analytics;

  // Quiz Management
  List<AdminQuizQuestion> _quizQuestions = [];
  String _selectedQuizCategory = '';
  String _selectedQuizDifficulty = '';

  // Redemption Management
  List<AdminRedemptionRequest> _redemptionRequests = [];
  String _selectedRedemptionStatus = 'all';

  // Tips Management
  List<AdminTip> _tips = [];
  String _selectedTipCategory = '';

  // User Management
  List<AdminUserInfo> _users = [];
  String _userSearchQuery = '';

  // Getters
  AdminLoadingState get state => _state;
  String? get errorMessage => _errorMessage;
  AdminAnalytics? get analytics => _analytics;
  List<AdminQuizQuestion> get quizQuestions => _quizQuestions;
  List<AdminRedemptionRequest> get redemptionRequests => _redemptionRequests;
  List<AdminTip> get tips => _tips;
  List<AdminUserInfo> get users => _users;
  String get selectedQuizCategory => _selectedQuizCategory;
  String get selectedQuizDifficulty => _selectedQuizDifficulty;
  String get selectedRedemptionStatus => _selectedRedemptionStatus;
  String get selectedTipCategory => _selectedTipCategory;
  String get userSearchQuery => _userSearchQuery;

  bool get isLoading => _state == AdminLoadingState.loading;
  bool get hasError => _state == AdminLoadingState.error;

  void _setState(AdminLoadingState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _setState(AdminLoadingState.error);
  }

  void clearError() {
    _errorMessage = null;
    _setState(AdminLoadingState.idle);
  }

  // ANALYTICS
  Future<void> loadAnalytics() async {
    try {
      _setState(AdminLoadingState.loading);
      _analytics = await AdminService.getAnalytics();
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to load analytics: ${e.toString()}');
    }
  }

  // QUIZ MANAGEMENT
  Future<void> loadQuizQuestions() async {
    try {
      _setState(AdminLoadingState.loading);
      _quizQuestions = await AdminService.getQuizQuestions(
        category: _selectedQuizCategory.isEmpty ? null : _selectedQuizCategory,
        difficulty: _selectedQuizDifficulty.isEmpty ? null : _selectedQuizDifficulty,
      );
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to load quiz questions: ${e.toString()}');
    }
  }

  Future<void> addQuizQuestion(AdminQuizQuestion question) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.addQuizQuestion(question);
      await loadQuizQuestions(); // Reload list
    } catch (e) {
      _setError('Failed to add quiz question: ${e.toString()}');
    }
  }

  Future<void> updateQuizQuestion(AdminQuizQuestion question) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.updateQuizQuestion(question);
      await loadQuizQuestions(); // Reload list
    } catch (e) {
      _setError('Failed to update quiz question: ${e.toString()}');
    }
  }

  Future<void> deleteQuizQuestion(String questionId) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.deleteQuizQuestion(questionId);
      await loadQuizQuestions(); // Reload list
    } catch (e) {
      _setError('Failed to delete quiz question: ${e.toString()}');
    }
  }

  void setQuizFilters({String? category, String? difficulty}) {
    if (category != null) _selectedQuizCategory = category;
    if (difficulty != null) _selectedQuizDifficulty = difficulty;
    notifyListeners();
    loadQuizQuestions();
  }

  // REDEMPTION MANAGEMENT
  Future<void> loadRedemptionRequests() async {
    try {
      _setState(AdminLoadingState.loading);
      _redemptionRequests = await AdminService.getRedemptionRequests(
        status: _selectedRedemptionStatus,
      );
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to load redemption requests: ${e.toString()}');
    }
  }

  Future<void> updateRedemptionStatus(
    String requestId,
    String status,
    String? adminNotes,
  ) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.updateRedemptionStatus(requestId, status, adminNotes);
      await loadRedemptionRequests(); // Reload list
    } catch (e) {
      _setError('Failed to update redemption status: ${e.toString()}');
    }
  }

  void setRedemptionStatusFilter(String status) {
    _selectedRedemptionStatus = status;
    notifyListeners();
    loadRedemptionRequests();
  }

  // TIPS MANAGEMENT
  Future<void> loadTips() async {
    try {
      _setState(AdminLoadingState.loading);
      _tips = await AdminService.getTips(
        category: _selectedTipCategory.isEmpty ? null : _selectedTipCategory,
      );
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to load tips: ${e.toString()}');
    }
  }

  Future<void> addTip(AdminTip tip) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.addTip(tip);
      await loadTips(); // Reload list
    } catch (e) {
      _setError('Failed to add tip: ${e.toString()}');
    }
  }

  Future<void> updateTip(AdminTip tip) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.updateTip(tip);
      await loadTips(); // Reload list
    } catch (e) {
      _setError('Failed to update tip: ${e.toString()}');
    }
  }

  Future<void> deleteTip(String tipId) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.deleteTip(tipId);
      await loadTips(); // Reload list
    } catch (e) {
      _setError('Failed to delete tip: ${e.toString()}');
    }
  }

  void setTipCategoryFilter(String category) {
    _selectedTipCategory = category;
    notifyListeners();
    loadTips();
  }

  // USER MANAGEMENT
  Future<void> loadUsers() async {
    try {
      _setState(AdminLoadingState.loading);
      _users = await AdminService.getUsers(
        searchEmail: _userSearchQuery.isEmpty ? null : _userSearchQuery,
      );
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to load users: ${e.toString()}');
    }
  }

  Future<void> banUser(String userId, bool isBanned) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.banUser(userId, isBanned);
      await loadUsers(); // Reload list
    } catch (e) {
      _setError('Failed to ${isBanned ? 'ban' : 'unban'} user: ${e.toString()}');
    }
  }

  Future<void> updateUserPoints(String userId, int points) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.updateUserPoints(userId, points);
      await loadUsers(); // Reload list
    } catch (e) {
      _setError('Failed to update user points: ${e.toString()}');
    }
  }

  void setUserSearchQuery(String query) {
    _userSearchQuery = query;
    notifyListeners();
  }

  Future<void> searchUsers() async {
    await loadUsers();
  }

  // LEADERBOARD MANAGEMENT
  Future<Map<String, dynamic>> getLeaderboard(String type) async {
    try {
      return await AdminService.getLeaderboard(type);
    } catch (e) {
      _setError('Failed to load leaderboard: ${e.toString()}');
      return {};
    }
  }

  Future<void> resetLeaderboard(String type) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.resetLeaderboard(type);
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to reset leaderboard: ${e.toString()}');
    }
  }

  Future<void> removeUserFromLeaderboard(String type, String userId) async {
    try {
      _setState(AdminLoadingState.loading);
      await AdminService.removeUserFromLeaderboard(type, userId);
      _setState(AdminLoadingState.success);
    } catch (e) {
      _setError('Failed to remove user from leaderboard: ${e.toString()}');
    }
  }
}
