import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../services/firebase_service.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _errorMessage;

  AuthState get state => _state;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _state == AuthState.authenticated && _user != null;
  bool get isLoading => _state == AuthState.loading;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    FirebaseService.authStateChanges.listen((User? firebaseUser) async {
      if (firebaseUser != null) {
        await _loadUserProfile(firebaseUser.uid);
      } else {
        _setState(AuthState.unauthenticated);
        _user = null;
      }
    });
  }

  Future<void> _loadUserProfile(String uid) async {
    try {
      _setState(AuthState.loading);
      final userProfile = await FirebaseService.getUserProfile(uid);
      if (userProfile != null) {
        _user = userProfile;
        _setState(AuthState.authenticated);
      } else {
        _setState(AuthState.error);
        _errorMessage = 'User profile not found';
      }
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = e.toString();
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    try {
      _setState(AuthState.loading);
      _clearError();
      
      final credential = await FirebaseService.signInWithEmail(email, password);
      if (credential?.user != null) {
        await _loadUserProfile(credential!.user!.uid);
        return true;
      }
      return false;
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      return false;
    }
  }

  Future<bool> signUpWithEmail(String email, String password, String name) async {
    try {
      _setState(AuthState.loading);
      _clearError();
      
      final credential = await FirebaseService.signUpWithEmail(email, password, name);
      if (credential?.user != null) {
        await _loadUserProfile(credential!.user!.uid);
        return true;
      }
      return false;
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      return false;
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      _setState(AuthState.loading);
      _clearError();
      
      final credential = await FirebaseService.signInWithGoogle();
      if (credential?.user != null) {
        await _loadUserProfile(credential!.user!.uid);
        return true;
      }
      return false;
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setState(AuthState.loading);
      await FirebaseService.signOut();
      _user = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = e.toString();
    }
  }

  Future<void> updateProfile(UserModel updatedUser) async {
    try {
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> refreshUserProfile() async {
    if (_user != null) {
      await _loadUserProfile(_user!.uid);
    }
  }

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  String _getErrorMessage(String error) {
    if (error.contains('user-not-found')) {
      return 'No user found with this email address.';
    } else if (error.contains('wrong-password')) {
      return 'Incorrect password.';
    } else if (error.contains('email-already-in-use')) {
      return 'An account already exists with this email address.';
    } else if (error.contains('weak-password')) {
      return 'Password is too weak.';
    } else if (error.contains('invalid-email')) {
      return 'Invalid email address.';
    } else if (error.contains('network-request-failed')) {
      return 'Network error. Please check your internet connection.';
    } else {
      return 'An error occurred. Please try again.';
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
