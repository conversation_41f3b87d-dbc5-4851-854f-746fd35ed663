import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:math';
import '../models/user_model.dart';
import '../services/firebase_service.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _errorMessage;
  bool _isGoogleSignInInProgress = false;

  AuthState get state => _state;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _state == AuthState.authenticated && _user != null;
  bool get isLoading => _state == AuthState.loading;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Clear any previous error state on initialization
    _clearError();

    FirebaseService.authStateChanges.listen((User? firebaseUser) async {
      if (firebaseUser != null) {
        await _loadUserProfile(firebaseUser.uid);
      } else {
        _setState(AuthState.unauthenticated);
        _user = null;
        _clearError(); // Clear errors when user signs out
      }
    });
  }

  Future<bool> _loadUserProfile(String uid) async {
    try {
      debugPrint('Loading user profile for UID: $uid');
      final currentUser = FirebaseService.currentUser;

      if (currentUser == null) {
        debugPrint('No current user found during profile loading');
        _setState(AuthState.error);
        _errorMessage = 'Authentication session expired';
        return false;
      }

      // Always create a working user profile to ensure app functionality
      _user = UserModel(
        uid: uid,
        name:
            currentUser.displayName ??
            currentUser.email?.split('@')[0] ??
            'User',
        email: currentUser.email ?? '',
        points: 100, // Welcome bonus
        avatar: Avatar(
          hair: 'short_black',
          clothing: 'casual_tshirt',
          background: 'city',
        ),
        lastLogin: DateTime.now(),
        dailyStreak: 1,
        referralCode: _generateTempReferralCode(),
        createdAt: DateTime.now(),
        unlockedAvatarItems: ['short_black', 'casual_tshirt', 'city'],
      );

      _setState(AuthState.authenticated);
      _clearError();
      debugPrint('User profile created successfully: ${_user!.name}');

      // Try to load/save profile from Firestore in background (non-blocking)
      _loadFirestoreProfileInBackground(uid);

      return true;
    } catch (e) {
      debugPrint('Error in _loadUserProfile: $e');
      _setState(AuthState.error);
      _errorMessage = 'Failed to load user profile';
      return false;
    }
  }

  void _loadFirestoreProfileInBackground(String uid) async {
    try {
      final firestoreProfile = await FirebaseService.getUserProfile(uid);
      if (firestoreProfile != null) {
        _user = firestoreProfile;
        notifyListeners();
        debugPrint('Firestore profile loaded and updated');
      } else {
        // Try to save current profile to Firestore
        if (_user != null) {
          await FirebaseService.updateUserProfile(_user!);
          debugPrint('User profile saved to Firestore');
        }
      }
    } catch (e) {
      debugPrint('Background Firestore operation failed: $e');
      // Don't change auth state - user is still authenticated
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    try {
      _setState(AuthState.loading);
      _clearError();

      debugPrint('Attempting to sign in with email: $email');

      // Check if user is already authenticated
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null && currentUser.email == email) {
        debugPrint('User already authenticated, loading profile');
        final success = await _loadUserProfile(currentUser.uid);
        if (success) {
          debugPrint(
            'Sign in completed successfully (user was already authenticated)',
          );
          return true;
        }
      }

      try {
        final credential = await FirebaseService.signInWithEmail(
          email,
          password,
        );

        if (credential?.user != null) {
          debugPrint('Sign in successful, loading user profile');
          final success = await _loadUserProfile(credential!.user!.uid);
          if (success) {
            debugPrint('Sign in completed successfully');
            return true;
          } else {
            debugPrint('Profile loading failed after successful sign in');
            _setState(AuthState.error);
            _errorMessage = 'Failed to load user profile. Please try again.';
            return false;
          }
        } else {
          debugPrint('Sign in returned null credential');
          _setState(AuthState.error);
          _errorMessage = 'Sign in failed. Please check your credentials.';
          return false;
        }
      } catch (e) {
        debugPrint('Firebase sign in error: ${e.toString()}');

        // Check if user was actually signed in despite the error
        final currentUser = FirebaseService.currentUser;
        if (currentUser != null && currentUser.email == email) {
          debugPrint('User was signed in despite error, loading profile');
          final success = await _loadUserProfile(currentUser.uid);
          if (success) {
            debugPrint('Sign in completed successfully (despite error)');
            return true;
          }
        }

        // If not authenticated, rethrow the original error
        rethrow;
      }
    } catch (e) {
      debugPrint('Sign in error: ${e.toString()}');

      // Final check: Even if there's an error, check if user is authenticated
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null && currentUser.email == email) {
        debugPrint(
          'User is authenticated despite final error, loading profile',
        );
        final success = await _loadUserProfile(currentUser.uid);
        if (success) {
          debugPrint('Sign in completed successfully (despite final error)');
          return true;
        }
      }

      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      return false;
    }
  }

  Future<bool> signUpWithEmail(
    String email,
    String password,
    String name,
  ) async {
    try {
      _setState(AuthState.loading);
      _clearError();

      debugPrint('Attempting to sign up with email: $email, name: $name');

      // Check if user is already authenticated (Firebase might have succeeded despite the error)
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null && currentUser.email == email) {
        debugPrint('User already authenticated, loading profile');
        final success = await _loadUserProfile(currentUser.uid);
        if (success) {
          debugPrint(
            'Sign up completed successfully (user was already authenticated)',
          );
          return true;
        }
      }

      try {
        final credential = await FirebaseService.signUpWithEmail(
          email,
          password,
          name,
        );

        if (credential?.user != null) {
          debugPrint('Sign up successful, loading user profile');
          final success = await _loadUserProfile(credential!.user!.uid);
          if (success) {
            debugPrint('Sign up completed successfully');
            return true;
          } else {
            debugPrint('Profile loading failed after successful sign up');
            _setState(AuthState.error);
            _errorMessage =
                'Account created but failed to set up profile. Please sign in.';
            return false;
          }
        } else {
          debugPrint('Sign up returned null credential');
          _setState(AuthState.error);
          _errorMessage = 'Sign up failed. Please try again.';
          return false;
        }
      } catch (e) {
        debugPrint('Firebase sign up error: ${e.toString()}');

        // Check if user was actually created despite the error
        final currentUser = FirebaseService.currentUser;
        if (currentUser != null && currentUser.email == email) {
          debugPrint('User was created despite error, loading profile');
          final success = await _loadUserProfile(currentUser.uid);
          if (success) {
            debugPrint('Sign up completed successfully (despite error)');
            return true;
          }
        }

        // If not authenticated, rethrow the original error
        rethrow;
      }
    } catch (e) {
      debugPrint('Sign up error: ${e.toString()}');

      // Final check: Even if there's an error, check if user is authenticated
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null && currentUser.email == email) {
        debugPrint(
          'User is authenticated despite final error, loading profile',
        );
        final success = await _loadUserProfile(currentUser.uid);
        if (success) {
          debugPrint('Sign up completed successfully (despite final error)');
          return true;
        }
      }

      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      return false;
    }
  }

  Future<bool> signInWithGoogle() async {
    // Prevent multiple simultaneous Google Sign-In attempts
    if (_isGoogleSignInInProgress) {
      debugPrint('Google Sign-In already in progress, ignoring request');
      return false;
    }

    try {
      _isGoogleSignInInProgress = true;
      _clearError();

      final credential = await FirebaseService.signInWithGoogle();

      // Check if user is signed in (either through credential or current user)
      final currentUser = FirebaseService.currentUser;
      if (credential?.user != null || currentUser != null) {
        final uid = credential?.user?.uid ?? currentUser!.uid;
        final result = await _loadUserProfile(uid);
        _isGoogleSignInInProgress = false;
        return result;
      }

      _setState(AuthState.error);
      _errorMessage = 'Google sign in failed';
      _isGoogleSignInInProgress = false;
      return false;
    } catch (e) {
      debugPrint('Google Sign-In error: ${e.toString()}');

      // For ANY Google Sign-In error, check if user is actually signed in
      // This handles all the various Google Sign-In plugin compatibility issues
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null) {
        debugPrint(
          'User is actually signed in despite error, proceeding with authentication',
        );
        final result = await _loadUserProfile(currentUser.uid);
        _isGoogleSignInInProgress = false;
        return result;
      }

      // Only show error if user is not signed in
      _setState(AuthState.error);
      _errorMessage = 'Unable to sign in. Please try again.';
      _isGoogleSignInInProgress = false;
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setState(AuthState.loading);
      _isGoogleSignInInProgress = false; // Reset flag on sign out
      await FirebaseService.signOut();
      _user = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = e.toString();
    }
  }

  Future<void> updateProfile(UserModel updatedUser) async {
    try {
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> refreshUserProfile() async {
    if (_user != null) {
      await _loadUserProfile(_user!.uid);
    }
  }

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearError() {
    if (_errorMessage != null) {
      debugPrint('Clearing error message: $_errorMessage');
    }
    _errorMessage = null;
  }

  String _getErrorMessage(String error) {
    debugPrint('Setting error message for error: $error');

    // Convert to lowercase for easier matching
    final lowerError = error.toLowerCase();

    if (lowerError.contains('user-not-found') ||
        lowerError.contains('user not found')) {
      return 'No account found with this email address.';
    } else if (lowerError.contains('wrong-password') ||
        lowerError.contains('invalid-credential')) {
      return 'Incorrect email or password.';
    } else if (lowerError.contains('email-already-in-use') ||
        lowerError.contains('email already in use')) {
      return 'An account already exists with this email address.';
    } else if (lowerError.contains('weak-password') ||
        lowerError.contains('password should be at least')) {
      return 'Password must be at least 6 characters long.';
    } else if (lowerError.contains('invalid-email') ||
        lowerError.contains('badly formatted')) {
      return 'Please enter a valid email address.';
    } else if (lowerError.contains('network-request-failed') ||
        lowerError.contains('network error')) {
      return 'Network error. Please check your internet connection.';
    } else if (lowerError.contains('too-many-requests')) {
      return 'Too many failed attempts. Please try again later.';
    } else if (lowerError.contains('operation-not-allowed')) {
      return 'Email/password sign-in is not enabled.';
    } else if (lowerError.contains('requires-recent-login')) {
      return 'Please sign out and sign in again to continue.';
    } else {
      debugPrint('Using generic error message for: $error');
      return 'Authentication failed. Please try again.';
    }
  }

  void clearError() {
    if (_errorMessage != null) {
      debugPrint('Manually clearing error message: $_errorMessage');
    }
    _errorMessage = null;
    notifyListeners();
  }

  String _generateTempReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        6,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }
}
