import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:math';
import '../models/user_model.dart';
import '../services/firebase_service.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _errorMessage;
  bool _isGoogleSignInInProgress = false;

  AuthState get state => _state;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _state == AuthState.authenticated && _user != null;
  bool get isLoading => _state == AuthState.loading;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Clear any previous error state on initialization
    _clearError();

    FirebaseService.authStateChanges.listen((User? firebaseUser) async {
      if (firebaseUser != null) {
        await _loadUserProfile(firebaseUser.uid);
      } else {
        _setState(AuthState.unauthenticated);
        _user = null;
        _clearError(); // Clear errors when user signs out
      }
    });
  }

  Future<bool> _loadUserProfile(String uid) async {
    try {
      _setState(AuthState.loading);
      final userProfile = await FirebaseService.getUserProfile(uid);
      if (userProfile != null) {
        _user = userProfile;
        _setState(AuthState.authenticated);
        _clearError(); // Clear any previous errors
        debugPrint('User profile loaded successfully: ${userProfile.name}');
        return true;
      } else {
        // If getUserProfile returns null, create a temporary profile
        final currentUser = FirebaseService.currentUser;
        if (currentUser != null) {
          debugPrint(
            'Creating temporary user profile for: ${currentUser.email}',
          );
          // Create a temporary user profile for testing
          _user = UserModel(
            uid: uid,
            name:
                currentUser.displayName ??
                currentUser.email?.split('@')[0] ??
                'User',
            email: currentUser.email ?? '',
            points: 100, // Welcome bonus
            avatar: Avatar(
              hair: 'short_black',
              clothing: 'casual_tshirt',
              background: 'city',
            ),
            lastLogin: DateTime.now(),
            dailyStreak: 1,
            referralCode: _generateTempReferralCode(),
            createdAt: DateTime.now(),
            unlockedAvatarItems: ['short_black', 'casual_tshirt', 'city'],
          );
          _setState(AuthState.authenticated);
          _clearError(); // Clear any previous errors
          debugPrint('Temporary user profile created successfully');
          return true;
        } else {
          _setState(AuthState.error);
          _errorMessage = 'Authentication failed';
          return false;
        }
      }
    } catch (e) {
      debugPrint('Error in _loadUserProfile: $e');
      // Even if there's an error, try to create a temporary profile
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null) {
        debugPrint('Creating fallback temporary user profile');
        _user = UserModel(
          uid: uid,
          name:
              currentUser.displayName ??
              currentUser.email?.split('@')[0] ??
              'User',
          email: currentUser.email ?? '',
          points: 100,
          avatar: Avatar(
            hair: 'short_black',
            clothing: 'casual_tshirt',
            background: 'city',
          ),
          lastLogin: DateTime.now(),
          dailyStreak: 1,
          referralCode: _generateTempReferralCode(),
          createdAt: DateTime.now(),
          unlockedAvatarItems: ['short_black', 'casual_tshirt', 'city'],
        );
        _setState(AuthState.authenticated);
        _clearError(); // Clear any previous errors
        debugPrint('Fallback user profile created successfully');
        return true;
      } else {
        _setState(AuthState.error);
        _errorMessage = 'Authentication failed';
        return false;
      }
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    try {
      _clearError();

      final credential = await FirebaseService.signInWithEmail(email, password);
      if (credential?.user != null) {
        return await _loadUserProfile(credential!.user!.uid);
      }
      _setState(AuthState.error);
      _errorMessage = 'Login failed';
      debugPrint('Setting error message: Login failed');
      return false;
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      debugPrint('Setting error message from exception: $_errorMessage');
      return false;
    }
  }

  Future<bool> signUpWithEmail(
    String email,
    String password,
    String name,
  ) async {
    try {
      _clearError();

      final credential = await FirebaseService.signUpWithEmail(
        email,
        password,
        name,
      );
      if (credential?.user != null) {
        return await _loadUserProfile(credential!.user!.uid);
      }
      _setState(AuthState.error);
      _errorMessage = 'Sign up failed';
      return false;
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = _getErrorMessage(e.toString());
      return false;
    }
  }

  Future<bool> signInWithGoogle() async {
    // Prevent multiple simultaneous Google Sign-In attempts
    if (_isGoogleSignInInProgress) {
      debugPrint('Google Sign-In already in progress, ignoring request');
      return false;
    }

    try {
      _isGoogleSignInInProgress = true;
      _clearError();

      final credential = await FirebaseService.signInWithGoogle();

      // Check if user is signed in (either through credential or current user)
      final currentUser = FirebaseService.currentUser;
      if (credential?.user != null || currentUser != null) {
        final uid = credential?.user?.uid ?? currentUser!.uid;
        final result = await _loadUserProfile(uid);
        _isGoogleSignInInProgress = false;
        return result;
      }

      _setState(AuthState.error);
      _errorMessage = 'Google sign in failed';
      _isGoogleSignInInProgress = false;
      return false;
    } catch (e) {
      debugPrint('Google Sign-In error: ${e.toString()}');

      // For ANY Google Sign-In error, check if user is actually signed in
      // This handles all the various Google Sign-In plugin compatibility issues
      final currentUser = FirebaseService.currentUser;
      if (currentUser != null) {
        debugPrint(
          'User is actually signed in despite error, proceeding with authentication',
        );
        final result = await _loadUserProfile(currentUser.uid);
        _isGoogleSignInInProgress = false;
        return result;
      }

      // Only show error if user is not signed in
      _setState(AuthState.error);
      _errorMessage = 'Unable to sign in. Please try again.';
      _isGoogleSignInInProgress = false;
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setState(AuthState.loading);
      _isGoogleSignInInProgress = false; // Reset flag on sign out
      await FirebaseService.signOut();
      _user = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setState(AuthState.error);
      _errorMessage = e.toString();
    }
  }

  Future<void> updateProfile(UserModel updatedUser) async {
    try {
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> refreshUserProfile() async {
    if (_user != null) {
      await _loadUserProfile(_user!.uid);
    }
  }

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearError() {
    if (_errorMessage != null) {
      debugPrint('Clearing error message: $_errorMessage');
    }
    _errorMessage = null;
  }

  String _getErrorMessage(String error) {
    debugPrint('Setting error message for error: $error');
    if (error.contains('user-not-found')) {
      return 'No user found with this email address.';
    } else if (error.contains('wrong-password')) {
      return 'Incorrect password.';
    } else if (error.contains('email-already-in-use')) {
      return 'An account already exists with this email address.';
    } else if (error.contains('weak-password')) {
      return 'Password is too weak.';
    } else if (error.contains('invalid-email')) {
      return 'Invalid email address.';
    } else if (error.contains('network-request-failed')) {
      return 'Network error. Please check your internet connection.';
    } else {
      debugPrint('Using generic error message for: $error');
      return 'An error occurred. Please try again.';
    }
  }

  void clearError() {
    if (_errorMessage != null) {
      debugPrint('Manually clearing error message: $_errorMessage');
    }
    _errorMessage = null;
    notifyListeners();
  }

  String _generateTempReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        6,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }
}
