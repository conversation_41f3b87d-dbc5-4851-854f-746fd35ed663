import 'package:flutter/foundation.dart';
import '../models/wallet_model.dart';
import '../models/user_model.dart';
import '../services/wallet_service.dart';

class WalletProvider extends ChangeNotifier {
  WalletModel? _wallet;
  List<RedemptionRequest> _redemptionHistory = [];
  List<MiniGameModel> _miniGames = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  WalletModel? get wallet => _wallet;
  List<RedemptionRequest> get redemptionHistory => _redemptionHistory;
  List<MiniGameModel> get miniGames => _miniGames;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Conversion helpers
  int pointsToRupees(int points) => WalletService.pointsToRupees(points);
  int pointsToDiamonds(int points) => WalletService.pointsToDiamonds(points);
  int rupeesToPoints(int rupees) => WalletService.rupeesToPoints(rupees);
  int diamondsToPoints(int diamonds) => WalletService.diamondsToPoints(diamonds);

  // Load user wallet
  Future<void> loadWallet(String userId) async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      _wallet = await WalletService.getUserWallet(userId);
      
      // Create wallet if it doesn't exist
      if (_wallet == null) {
        _wallet = WalletModel(
          userId: userId,
          totalPoints: 0,
          totalDiamonds: 0,
          totalRedemptions: 0,
          lastUpdated: DateTime.now(),
        );
        await WalletService.updateWallet(_wallet!);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Load redemption history
  Future<void> loadRedemptionHistory(String userId) async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      _redemptionHistory = await WalletService.getUserRedemptions(userId);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Load mini games
  Future<void> loadMiniGames() async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      _miniGames = await WalletService.getMiniGames();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Submit redemption request
  Future<bool> submitRedemptionRequest({
    required UserModel user,
    required RedemptionType type,
    required int amount,
    String? paymentDetails,
    String? gameId,
  }) async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      final requestId = await WalletService.submitRedemptionRequest(
        user: user,
        type: type,
        amount: amount,
        paymentDetails: paymentDetails,
        gameId: gameId,
      );

      // Reload redemption history
      await loadRedemptionHistory(user.uid);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Validate redemption
  String? validateRedemption({
    required RedemptionType type,
    required int amount,
    required int userPoints,
  }) {
    int pointsNeeded;
    if (type == RedemptionType.rupees) {
      pointsNeeded = rupeesToPoints(amount);
    } else {
      pointsNeeded = diamondsToPoints(amount);
    }

    return WalletService.validateRedemptionRequest(
      type: type,
      points: pointsNeeded,
      amount: amount,
      userPoints: userPoints,
    );
  }

  // Get enabled mini games
  List<MiniGameModel> get enabledMiniGames {
    return _miniGames.where((game) => game.isEnabled).toList();
  }

  // Get mini game by ID
  MiniGameModel? getMiniGameById(String id) {
    try {
      return _miniGames.firstWhere((game) => game.id == id);
    } catch (e) {
      return null;
    }
  }

  // Submit support ticket
  Future<bool> submitSupportTicket({
    required UserModel user,
    required String subject,
    required String message,
  }) async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      await WalletService.submitSupportTicket(
        user: user,
        subject: subject,
        message: message,
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update wallet after points change
  Future<void> updateWalletPoints(String userId, int newPoints) async {
    if (_wallet != null) {
      _wallet = _wallet!.copyWith(
        totalPoints: newPoints,
        lastUpdated: DateTime.now(),
      );
      
      try {
        await WalletService.updateWallet(_wallet!);
        notifyListeners();
      } catch (e) {
        debugPrint('Error updating wallet points: $e');
      }
    }
  }

  // Update wallet after diamond change
  Future<void> updateWalletDiamonds(String userId, int newDiamonds) async {
    if (_wallet != null) {
      _wallet = _wallet!.copyWith(
        totalDiamonds: newDiamonds,
        lastUpdated: DateTime.now(),
      );
      
      try {
        await WalletService.updateWallet(_wallet!);
        notifyListeners();
      } catch (e) {
        debugPrint('Error updating wallet diamonds: $e');
      }
    }
  }

  // Clear error
  void _clearError() {
    _errorMessage = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  // Reset provider
  void reset() {
    _wallet = null;
    _redemptionHistory = [];
    _miniGames = [];
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}
