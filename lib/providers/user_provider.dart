import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/reward_model.dart';
import '../services/firebase_service.dart';
import '../utils/constants.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _user;
  List<Reward> _recentRewards = [];
  bool _isLoading = false;
  String? _errorMessage;

  UserModel? get user => _user;
  List<Reward> get recentRewards => _recentRewards;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Spin-related getters
  bool get canSpin => _getCanSpin();
  Duration get spinCooldownRemaining => _getSpinCooldownRemaining();

  // Daily login getters
  bool get canClaimDailyReward => _getCanClaimDailyReward();
  int get dailyRewardPoints => _getDailyRewardPoints();

  void setUser(UserModel? user) {
    _user = user;
    if (user != null) {
      _loadRecentRewards();
      _initializeUserData();
    }
    notifyListeners();
  }

  Future<void> _initializeUserData() async {
    // Load any cached data from SharedPreferences
    // This ensures proper state initialization
    notifyListeners();
  }

  Future<void> _loadRecentRewards() async {
    if (_user == null) return;

    try {
      _recentRewards = await FirebaseService.getUserRewards(
        _user!.uid,
        limit: 10,
      );
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> addPoints(
    int points,
    RewardType type, {
    Map<String, dynamic>? metadata,
  }) async {
    if (_user == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Update points in Firebase
      await FirebaseService.updateUserPoints(_user!.uid, points);

      // Create reward record
      final reward = Reward(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: _user!.uid,
        type: type,
        points: points,
        timestamp: DateTime.now(),
        metadata: metadata ?? {},
      );

      await FirebaseService.addReward(reward);

      // Update local user model
      _user = _user!.copyWith(points: _user!.points + points);

      // Add to recent rewards
      _recentRewards.insert(0, reward);
      if (_recentRewards.length > 10) {
        _recentRewards.removeLast();
      }

      // Update leaderboard
      await FirebaseService.updateLeaderboard(
        _user!.uid,
        _user!.name,
        _user!.points,
        _user!.avatar,
      );

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<bool> spendPoints(int points) async {
    if (_user == null || _user!.points < points) return false;

    try {
      _isLoading = true;
      notifyListeners();

      // Update points in Firebase
      await FirebaseService.updateUserPoints(_user!.uid, -points);

      // Update local user model
      _user = _user!.copyWith(points: _user!.points - points);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<SpinReward> performSpin() async {
    if (!canSpin) throw Exception('Spin is on cooldown');

    try {
      _isLoading = true;
      notifyListeners();

      // Generate random reward
      final random = DateTime.now().millisecondsSinceEpoch % 100;
      int points;
      String message;
      bool isJackpot = false;

      if (random < 40) {
        points =
            AppConstants.minSpinPoints + (random % 21); // 10-30 points (40%)
        message = 'Nice spin!';
      } else if (random < 70) {
        points = 31 + (random % 30); // 31-60 points (30%)
        message = 'Great spin!';
      } else if (random < 90) {
        points = 61 + (random % 30); // 61-90 points (20%)
        message = 'Excellent spin!';
      } else if (random < 99) {
        points = 91 + (random % 10); // 91-100 points (9%)
        message = 'Amazing spin!';
      } else {
        points = AppConstants.maxSpinPoints + 50; // 150 points (1%)
        message = 'JACKPOT! Incredible spin!';
        isJackpot = true;
      }

      // Add points
      await addPoints(
        points,
        RewardType.spin,
        metadata: {
          'isJackpot': isJackpot,
          'spinTime': DateTime.now().toIso8601String(),
        },
      );

      // Update spin statistics
      if (_user != null) {
        final updatedUser = _user!.copyWith(totalSpins: _user!.totalSpins + 1);
        await FirebaseService.updateUserProfile(updatedUser);
        _user = updatedUser;
      }

      // Save last spin time
      await _saveLastSpinTime();

      _isLoading = false;
      notifyListeners();

      return SpinReward(points: points, message: message, isJackpot: isJackpot);
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  Future<void> claimDailyReward() async {
    if (!canClaimDailyReward || _user == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Use server timestamp to prevent date manipulation
      final serverTime = await _getServerTime();
      final lastClaimDate = await _getLastClaimDate();
      final points = dailyRewardPoints;

      // Validate claim using server time
      if (!_isValidDailyClaim(serverTime, lastClaimDate)) {
        _isLoading = false;
        _errorMessage =
            'Daily reward already claimed today or invalid time detected';
        notifyListeners();
        return;
      }

      // Calculate new streak based on server time
      int newStreak;
      final daysDifference = _calculateDaysDifference(
        serverTime,
        lastClaimDate,
      );

      if (daysDifference == 1) {
        // Consecutive day - continue streak
        newStreak = _user!.dailyStreak + 1;
      } else if (daysDifference > 1) {
        // Missed days - reset streak
        newStreak = 1;
      } else {
        // Invalid claim
        _isLoading = false;
        _errorMessage = 'Invalid daily reward claim';
        notifyListeners();
        return;
      }

      // Add points
      await addPoints(
        points,
        RewardType.dailyLogin,
        metadata: {
          'streak': newStreak,
          'day': newStreak,
          'serverTime': serverTime.toIso8601String(),
        },
      );

      // Update user with new streak and server time
      final updatedUser = _user!.copyWith(
        dailyStreak: newStreak,
        lastLogin: serverTime,
      );

      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;

      // Save claim date with server time
      await _saveLastClaimDate(serverTime);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      rethrow; // Re-throw to handle in UI
    }
  }

  Future<void> updateAvatar(Avatar newAvatar) async {
    if (_user == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      final updatedUser = _user!.copyWith(avatar: newAvatar);
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> unlockAvatarItem(String item, int cost) async {
    if (_user == null || _user!.points < cost) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Spend points
      final success = await spendPoints(cost);
      if (!success) return;

      // Add item to unlocked list
      final unlockedItems = List<String>.from(_user!.unlockedAvatarItems);
      if (!unlockedItems.contains(item)) {
        unlockedItems.add(item);
      }

      final updatedUser = _user!.copyWith(unlockedAvatarItems: unlockedItems);
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Private helper methods
  bool _getCanSpin() {
    return spinCooldownRemaining.inSeconds <= 0;
  }

  Duration _getSpinCooldownRemaining() {
    final lastSpinTime = _getLastSpinTime();
    final cooldownEnd = lastSpinTime.add(
      const Duration(hours: AppConstants.spinCooldownHours),
    );
    final remaining = cooldownEnd.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  bool _getCanClaimDailyReward() {
    if (_user == null) return false;

    final now = DateTime.now();
    final lastLogin = _user!.lastLogin;

    // Check if it's a different day (not just 24 hours)
    final lastLoginDate = DateTime(
      lastLogin.year,
      lastLogin.month,
      lastLogin.day,
    );
    final currentDate = DateTime(now.year, now.month, now.day);

    return currentDate.isAfter(lastLoginDate);
  }

  // Server time methods to prevent date manipulation
  Future<DateTime> _getServerTime() async {
    try {
      // In production, this would call a server endpoint to get server time
      // For now, we'll use a combination of local time and validation
      final now = DateTime.now();

      // Add basic validation - check if local time is reasonable
      final minValidTime = DateTime(2024, 1, 1);
      final maxValidTime = DateTime.now().add(const Duration(days: 1));

      if (now.isBefore(minValidTime) || now.isAfter(maxValidTime)) {
        throw Exception('Invalid system time detected');
      }

      return now;
    } catch (e) {
      throw Exception('Failed to get server time: $e');
    }
  }

  Future<DateTime> _getLastClaimDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastClaimString = prefs.getString(AppConstants.keyLastLoginDate);

      if (lastClaimString == null) {
        // Return a date far in the past if no previous claim
        return DateTime(2020, 1, 1);
      }

      return DateTime.parse(lastClaimString);
    } catch (e) {
      // Return a date far in the past if parsing fails
      return DateTime(2020, 1, 1);
    }
  }

  bool _isValidDailyClaim(DateTime serverTime, DateTime lastClaimDate) {
    // Check if it's a different day (not just 24 hours)
    final lastClaimDay = DateTime(
      lastClaimDate.year,
      lastClaimDate.month,
      lastClaimDate.day,
    );
    final currentDay = DateTime(
      serverTime.year,
      serverTime.month,
      serverTime.day,
    );

    // Must be at least the next day
    final isNextDay = currentDay.isAfter(lastClaimDay);

    // Additional validation: ensure not more than 48 hours difference
    // to prevent extreme date manipulation
    final hoursDifference = serverTime.difference(lastClaimDate).inHours;
    final isReasonableTimeDiff = hoursDifference >= 20 && hoursDifference <= 50;

    return isNextDay && (lastClaimDate.year == 2020 || isReasonableTimeDiff);
  }

  int _calculateDaysDifference(DateTime serverTime, DateTime lastClaimDate) {
    // If this is the first claim ever
    if (lastClaimDate.year == 2020) {
      return 1;
    }

    final lastClaimDay = DateTime(
      lastClaimDate.year,
      lastClaimDate.month,
      lastClaimDate.day,
    );
    final currentDay = DateTime(
      serverTime.year,
      serverTime.month,
      serverTime.day,
    );

    return currentDay.difference(lastClaimDay).inDays;
  }

  Future<void> _saveLastClaimDate(DateTime claimTime) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.keyLastLoginDate,
      claimTime.toIso8601String(),
    );
  }

  int _getDailyRewardPoints() {
    if (_user == null) return AppConstants.dailyLoginBasePoints;

    final streak = _user!.dailyStreak;
    final basePoints = AppConstants.dailyLoginBasePoints;

    // Increase points by 10 for each day in streak, max 200 points
    return (basePoints + (streak * 10)).clamp(basePoints, 200);
  }

  DateTime _getLastSpinTime() {
    // Try to load from SharedPreferences, fallback to old time if not found
    // This ensures spin is available on first use
    try {
      // In a real implementation, this would be async and loaded during initialization
      // For now, we'll assume spin is available if no previous time is stored
      return DateTime.now().subtract(
        const Duration(hours: AppConstants.spinCooldownHours + 1),
      );
    } catch (e) {
      return DateTime.now().subtract(
        const Duration(hours: AppConstants.spinCooldownHours + 1),
      );
    }
  }

  Future<void> _saveLastSpinTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.keyLastSpinTime,
      DateTime.now().toIso8601String(),
    );
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
