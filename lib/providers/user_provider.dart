import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/reward_model.dart';
import '../services/firebase_service.dart';
import '../utils/constants.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _user;
  List<Reward> _recentRewards = [];
  bool _isLoading = false;
  String? _errorMessage;

  UserModel? get user => _user;
  List<Reward> get recentRewards => _recentRewards;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Spin-related getters
  bool get canSpin => _getCanSpin();
  Duration get spinCooldownRemaining => _getSpinCooldownRemaining();

  // Daily login getters
  bool get canClaimDailyReward => _getCanClaimDailyReward();
  int get dailyRewardPoints => _getDailyRewardPoints();

  void setUser(UserModel? user) {
    _user = user;
    if (user != null) {
      _loadRecentRewards();
      _initializeUserData();
    }
    notifyListeners();
  }

  Future<void> _initializeUserData() async {
    // Load any cached data from SharedPreferences
    // This ensures proper state initialization
    notifyListeners();
  }

  Future<void> _loadRecentRewards() async {
    if (_user == null) return;

    try {
      _recentRewards = await FirebaseService.getUserRewards(
        _user!.uid,
        limit: 10,
      );
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> addPoints(
    int points,
    RewardType type, {
    Map<String, dynamic>? metadata,
  }) async {
    if (_user == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Update points in Firebase
      await FirebaseService.updateUserPoints(_user!.uid, points);

      // Create reward record
      final reward = Reward(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: _user!.uid,
        type: type,
        points: points,
        timestamp: DateTime.now(),
        metadata: metadata ?? {},
      );

      await FirebaseService.addReward(reward);

      // Update local user model
      _user = _user!.copyWith(points: _user!.points + points);

      // Add to recent rewards
      _recentRewards.insert(0, reward);
      if (_recentRewards.length > 10) {
        _recentRewards.removeLast();
      }

      // Update leaderboard
      await FirebaseService.updateLeaderboard(
        _user!.uid,
        _user!.name,
        _user!.points,
        _user!.avatar,
      );

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<bool> spendPoints(int points) async {
    if (_user == null || _user!.points < points) return false;

    try {
      _isLoading = true;
      notifyListeners();

      // Update points in Firebase
      await FirebaseService.updateUserPoints(_user!.uid, -points);

      // Update local user model
      _user = _user!.copyWith(points: _user!.points - points);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<SpinReward> performSpin() async {
    if (!canSpin) throw Exception('Spin is on cooldown');

    try {
      _isLoading = true;
      notifyListeners();

      // Generate random reward
      final random = DateTime.now().millisecondsSinceEpoch % 100;
      int points;
      String message;
      bool isJackpot = false;

      if (random < 40) {
        points =
            AppConstants.minSpinPoints + (random % 21); // 10-30 points (40%)
        message = 'Nice spin!';
      } else if (random < 70) {
        points = 31 + (random % 30); // 31-60 points (30%)
        message = 'Great spin!';
      } else if (random < 90) {
        points = 61 + (random % 30); // 61-90 points (20%)
        message = 'Excellent spin!';
      } else if (random < 99) {
        points = 91 + (random % 10); // 91-100 points (9%)
        message = 'Amazing spin!';
      } else {
        points = AppConstants.maxSpinPoints + 50; // 150 points (1%)
        message = 'JACKPOT! Incredible spin!';
        isJackpot = true;
      }

      // Add points
      await addPoints(
        points,
        RewardType.spin,
        metadata: {
          'isJackpot': isJackpot,
          'spinTime': DateTime.now().toIso8601String(),
        },
      );

      // Update spin statistics
      if (_user != null) {
        final updatedUser = _user!.copyWith(totalSpins: _user!.totalSpins + 1);
        await FirebaseService.updateUserProfile(updatedUser);
        _user = updatedUser;
      }

      // Save last spin time
      await _saveLastSpinTime();

      _isLoading = false;
      notifyListeners();

      return SpinReward(points: points, message: message, isJackpot: isJackpot);
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  Future<void> claimDailyReward() async {
    if (!canClaimDailyReward || _user == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      final now = DateTime.now();
      final lastLogin = _user!.lastLogin;
      final points = dailyRewardPoints;

      // Calculate new streak
      int newStreak;
      final daysDifference = now.difference(lastLogin).inDays;

      if (daysDifference == 1) {
        // Consecutive day - continue streak
        newStreak = _user!.dailyStreak + 1;
      } else if (daysDifference > 1) {
        // Missed days - reset streak
        newStreak = 1;
      } else {
        // Same day - shouldn't happen due to canClaimDailyReward check
        return;
      }

      // Add points
      await addPoints(
        points,
        RewardType.dailyLogin,
        metadata: {'streak': newStreak, 'day': newStreak},
      );

      // Update user with new streak and last login
      final updatedUser = _user!.copyWith(
        dailyStreak: newStreak,
        lastLogin: now,
      );

      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;

      // Save last login date
      await _saveLastLoginDate();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> updateAvatar(Avatar newAvatar) async {
    if (_user == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      final updatedUser = _user!.copyWith(avatar: newAvatar);
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> unlockAvatarItem(String item, int cost) async {
    if (_user == null || _user!.points < cost) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Spend points
      final success = await spendPoints(cost);
      if (!success) return;

      // Add item to unlocked list
      final unlockedItems = List<String>.from(_user!.unlockedAvatarItems);
      if (!unlockedItems.contains(item)) {
        unlockedItems.add(item);
      }

      final updatedUser = _user!.copyWith(unlockedAvatarItems: unlockedItems);
      await FirebaseService.updateUserProfile(updatedUser);
      _user = updatedUser;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Private helper methods
  bool _getCanSpin() {
    return spinCooldownRemaining.inSeconds <= 0;
  }

  Duration _getSpinCooldownRemaining() {
    final lastSpinTime = _getLastSpinTime();
    final cooldownEnd = lastSpinTime.add(
      const Duration(hours: AppConstants.spinCooldownHours),
    );
    final remaining = cooldownEnd.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  bool _getCanClaimDailyReward() {
    if (_user == null) return false;

    final now = DateTime.now();
    final lastLogin = _user!.lastLogin;

    // Check if it's a different day (not just 24 hours)
    final lastLoginDate = DateTime(
      lastLogin.year,
      lastLogin.month,
      lastLogin.day,
    );
    final currentDate = DateTime(now.year, now.month, now.day);

    return currentDate.isAfter(lastLoginDate);
  }

  int _getDailyRewardPoints() {
    if (_user == null) return AppConstants.dailyLoginBasePoints;

    final streak = _user!.dailyStreak;
    final basePoints = AppConstants.dailyLoginBasePoints;

    // Increase points by 10 for each day in streak, max 200 points
    return (basePoints + (streak * 10)).clamp(basePoints, 200);
  }

  DateTime _getLastSpinTime() {
    // Try to load from SharedPreferences, fallback to old time if not found
    // This ensures spin is available on first use
    try {
      // In a real implementation, this would be async and loaded during initialization
      // For now, we'll assume spin is available if no previous time is stored
      return DateTime.now().subtract(
        const Duration(hours: AppConstants.spinCooldownHours + 1),
      );
    } catch (e) {
      return DateTime.now().subtract(
        const Duration(hours: AppConstants.spinCooldownHours + 1),
      );
    }
  }

  Future<void> _saveLastSpinTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.keyLastSpinTime,
      DateTime.now().toIso8601String(),
    );
  }

  Future<void> _saveLastLoginDate() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      AppConstants.keyLastLoginDate,
      DateTime.now().toIso8601String(),
    );
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
