import 'package:cloud_firestore/cloud_firestore.dart';

enum RedemptionType { rupees, diamonds }
enum RedemptionStatus { pending, approved, rejected, completed, cancelled }

class RedemptionRequest {
  final String id;
  final String userId;
  final String userName;
  final String userEmail;
  final RedemptionType type;
  final int pointsUsed;
  final double amount; // Rupees or diamond count
  final RedemptionStatus status;
  final DateTime requestDate;
  final DateTime? processedDate;
  final DateTime? completedDate;
  final String? processedBy; // Admin who processed
  final String? adminNotes;
  final String? rejectionReason;
  final String? upiId; // For rupee withdrawals
  final String? gameId; // For diamond withdrawals
  final String? phoneNumber; // For verification
  final String? transactionId; // Payment transaction ID
  final Map<String, dynamic> paymentDetails;

  RedemptionRequest({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.type,
    required this.pointsUsed,
    required this.amount,
    this.status = RedemptionStatus.pending,
    required this.requestDate,
    this.processedDate,
    this.completedDate,
    this.processedBy,
    this.adminNotes,
    this.rejectionReason,
    this.upiId,
    this.gameId,
    this.phoneNumber,
    this.transactionId,
    this.paymentDetails = const {},
  });

  factory RedemptionRequest.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return RedemptionRequest(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userEmail: data['userEmail'] ?? '',
      type: RedemptionType.values.firstWhere(
        (e) => e.toString().split('.').last == data['type'],
        orElse: () => RedemptionType.rupees,
      ),
      pointsUsed: data['pointsUsed'] ?? 0,
      amount: (data['amount'] ?? 0.0).toDouble(),
      status: RedemptionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == data['status'],
        orElse: () => RedemptionStatus.pending,
      ),
      requestDate: (data['requestDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      processedDate: (data['processedDate'] as Timestamp?)?.toDate(),
      completedDate: (data['completedDate'] as Timestamp?)?.toDate(),
      processedBy: data['processedBy'],
      adminNotes: data['adminNotes'],
      rejectionReason: data['rejectionReason'],
      upiId: data['upiId'],
      gameId: data['gameId'],
      phoneNumber: data['phoneNumber'],
      transactionId: data['transactionId'],
      paymentDetails: Map<String, dynamic>.from(data['paymentDetails'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'type': type.toString().split('.').last,
      'pointsUsed': pointsUsed,
      'amount': amount,
      'status': status.toString().split('.').last,
      'requestDate': Timestamp.fromDate(requestDate),
      'processedDate': processedDate != null ? Timestamp.fromDate(processedDate!) : null,
      'completedDate': completedDate != null ? Timestamp.fromDate(completedDate!) : null,
      'processedBy': processedBy,
      'adminNotes': adminNotes,
      'rejectionReason': rejectionReason,
      'upiId': upiId,
      'gameId': gameId,
      'phoneNumber': phoneNumber,
      'transactionId': transactionId,
      'paymentDetails': paymentDetails,
    };
  }

  RedemptionRequest copyWith({
    RedemptionStatus? status,
    DateTime? processedDate,
    DateTime? completedDate,
    String? processedBy,
    String? adminNotes,
    String? rejectionReason,
    String? transactionId,
    Map<String, dynamic>? paymentDetails,
  }) {
    return RedemptionRequest(
      id: id,
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      type: type,
      pointsUsed: pointsUsed,
      amount: amount,
      status: status ?? this.status,
      requestDate: requestDate,
      processedDate: processedDate ?? this.processedDate,
      completedDate: completedDate ?? this.completedDate,
      processedBy: processedBy ?? this.processedBy,
      adminNotes: adminNotes ?? this.adminNotes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      upiId: upiId,
      gameId: gameId,
      phoneNumber: phoneNumber,
      transactionId: transactionId ?? this.transactionId,
      paymentDetails: paymentDetails ?? this.paymentDetails,
    );
  }

  String get typeDisplayName {
    switch (type) {
      case RedemptionType.diamonds:
        return 'Free Fire Diamonds';
      case RedemptionType.rupees:
        return 'Indian Rupees (INR)';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case RedemptionStatus.pending:
        return 'Pending Review';
      case RedemptionStatus.approved:
        return 'Approved';
      case RedemptionStatus.rejected:
        return 'Rejected';
      case RedemptionStatus.completed:
        return 'Completed';
      case RedemptionStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get amountDisplayText {
    switch (type) {
      case RedemptionType.rupees:
        return '₹${amount.toStringAsFixed(2)}';
      case RedemptionType.diamonds:
        return '${amount.toInt()} 💎';
    }
  }

  bool get canBeCancelled {
    return status == RedemptionStatus.pending;
  }

  bool get isProcessed {
    return status != RedemptionStatus.pending;
  }

  bool get isCompleted {
    return status == RedemptionStatus.completed;
  }

  bool get isSuccessful {
    return status == RedemptionStatus.completed || status == RedemptionStatus.approved;
  }

  String get paymentMethod {
    switch (type) {
      case RedemptionType.rupees:
        return upiId ?? 'UPI';
      case RedemptionType.diamonds:
        return 'Free Fire Game ID: ${gameId ?? 'Not provided'}';
    }
  }
}

// User Profile Model for storing payment details
class UserPaymentProfile {
  final String userId;
  final String? upiId;
  final String? gameId;
  final String? phoneNumber;
  final String? bankAccount;
  final String? ifscCode;
  final String? accountHolderName;
  final bool isVerified;
  final DateTime lastUpdated;

  UserPaymentProfile({
    required this.userId,
    this.upiId,
    this.gameId,
    this.phoneNumber,
    this.bankAccount,
    this.ifscCode,
    this.accountHolderName,
    this.isVerified = false,
    required this.lastUpdated,
  });

  factory UserPaymentProfile.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return UserPaymentProfile(
      userId: doc.id,
      upiId: data['upiId'],
      gameId: data['gameId'],
      phoneNumber: data['phoneNumber'],
      bankAccount: data['bankAccount'],
      ifscCode: data['ifscCode'],
      accountHolderName: data['accountHolderName'],
      isVerified: data['isVerified'] ?? false,
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'upiId': upiId,
      'gameId': gameId,
      'phoneNumber': phoneNumber,
      'bankAccount': bankAccount,
      'ifscCode': ifscCode,
      'accountHolderName': accountHolderName,
      'isVerified': isVerified,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  UserPaymentProfile copyWith({
    String? upiId,
    String? gameId,
    String? phoneNumber,
    String? bankAccount,
    String? ifscCode,
    String? accountHolderName,
    bool? isVerified,
    DateTime? lastUpdated,
  }) {
    return UserPaymentProfile(
      userId: userId,
      upiId: upiId ?? this.upiId,
      gameId: gameId ?? this.gameId,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      bankAccount: bankAccount ?? this.bankAccount,
      ifscCode: ifscCode ?? this.ifscCode,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      isVerified: isVerified ?? this.isVerified,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasUpiDetails => upiId != null && upiId!.isNotEmpty;
  bool get hasGameId => gameId != null && gameId!.isNotEmpty;
  bool get hasBankDetails => bankAccount != null && ifscCode != null && accountHolderName != null;
  bool get canRedeemRupees => hasUpiDetails || hasBankDetails;
  bool get canRedeemDiamonds => hasGameId;
}
