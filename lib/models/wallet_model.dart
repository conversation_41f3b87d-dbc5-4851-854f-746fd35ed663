import 'package:cloud_firestore/cloud_firestore.dart';

enum RedemptionType { diamonds, rupees }
enum RedemptionStatus { pending, approved, rejected, processing }

class WalletModel {
  final String userId;
  final int totalPoints;
  final int totalDiamonds;
  final int totalRedemptions;
  final DateTime lastUpdated;

  WalletModel({
    required this.userId,
    required this.totalPoints,
    required this.totalDiamonds,
    required this.totalRedemptions,
    required this.lastUpdated,
  });

  factory WalletModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return WalletModel(
      userId: doc.id,
      totalPoints: data['totalPoints'] ?? 0,
      totalDiamonds: data['totalDiamonds'] ?? 0,
      totalRedemptions: data['totalRedemptions'] ?? 0,
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'totalPoints': totalPoints,
      'totalDiamonds': totalDiamonds,
      'totalRedemptions': totalRedemptions,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  WalletModel copyWith({
    int? totalPoints,
    int? totalDiamonds,
    int? totalRedemptions,
    DateTime? lastUpdated,
  }) {
    return WalletModel(
      userId: userId,
      totalPoints: totalPoints ?? this.totalPoints,
      totalDiamonds: totalDiamonds ?? this.totalDiamonds,
      totalRedemptions: totalRedemptions ?? this.totalRedemptions,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class RedemptionRequest {
  final String id;
  final String userId;
  final String userName;
  final String userEmail;
  final RedemptionType type;
  final int pointsUsed;
  final int amount; // diamonds or rupees
  final RedemptionStatus status;
  final DateTime requestDate;
  final DateTime? processedDate;
  final String? adminNotes;
  final String? paymentDetails; // For rupee redemptions
  final String? gameId; // For diamond redemptions

  RedemptionRequest({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.type,
    required this.pointsUsed,
    required this.amount,
    required this.status,
    required this.requestDate,
    this.processedDate,
    this.adminNotes,
    this.paymentDetails,
    this.gameId,
  });

  factory RedemptionRequest.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return RedemptionRequest(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userEmail: data['userEmail'] ?? '',
      type: RedemptionType.values.firstWhere(
        (e) => e.toString() == 'RedemptionType.${data['type']}',
        orElse: () => RedemptionType.diamonds,
      ),
      pointsUsed: data['pointsUsed'] ?? 0,
      amount: data['amount'] ?? 0,
      status: RedemptionStatus.values.firstWhere(
        (e) => e.toString() == 'RedemptionStatus.${data['status']}',
        orElse: () => RedemptionStatus.pending,
      ),
      requestDate: (data['requestDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      processedDate: (data['processedDate'] as Timestamp?)?.toDate(),
      adminNotes: data['adminNotes'],
      paymentDetails: data['paymentDetails'],
      gameId: data['gameId'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'type': type.toString().split('.').last,
      'pointsUsed': pointsUsed,
      'amount': amount,
      'status': status.toString().split('.').last,
      'requestDate': Timestamp.fromDate(requestDate),
      'processedDate': processedDate != null ? Timestamp.fromDate(processedDate!) : null,
      'adminNotes': adminNotes,
      'paymentDetails': paymentDetails,
      'gameId': gameId,
    };
  }

  RedemptionRequest copyWith({
    RedemptionStatus? status,
    DateTime? processedDate,
    String? adminNotes,
    String? paymentDetails,
  }) {
    return RedemptionRequest(
      id: id,
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      type: type,
      pointsUsed: pointsUsed,
      amount: amount,
      status: status ?? this.status,
      requestDate: requestDate,
      processedDate: processedDate ?? this.processedDate,
      adminNotes: adminNotes ?? this.adminNotes,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      gameId: gameId,
    );
  }

  String get typeDisplayName {
    switch (type) {
      case RedemptionType.diamonds:
        return 'Free Fire Diamonds';
      case RedemptionType.rupees:
        return 'Indian Rupees (INR)';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case RedemptionStatus.pending:
        return 'Pending Review';
      case RedemptionStatus.approved:
        return 'Approved';
      case RedemptionStatus.rejected:
        return 'Rejected';
      case RedemptionStatus.processing:
        return 'Processing';
    }
  }
}

class MiniGameModel {
  final String id;
  final String name;
  final String description;
  final bool isEnabled;
  final int minPoints;
  final int maxPoints;
  final String rules;
  final int gamesBeforeAd;
  final DateTime lastUpdated;

  MiniGameModel({
    required this.id,
    required this.name,
    required this.description,
    required this.isEnabled,
    required this.minPoints,
    required this.maxPoints,
    required this.rules,
    this.gamesBeforeAd = 3,
    required this.lastUpdated,
  });

  factory MiniGameModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return MiniGameModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      isEnabled: data['isEnabled'] ?? false,
      minPoints: data['minPoints'] ?? 20,
      maxPoints: data['maxPoints'] ?? 50,
      rules: data['rules'] ?? '',
      gamesBeforeAd: data['gamesBeforeAd'] ?? 3,
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'isEnabled': isEnabled,
      'minPoints': minPoints,
      'maxPoints': maxPoints,
      'rules': rules,
      'gamesBeforeAd': gamesBeforeAd,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  MiniGameModel copyWith({
    String? name,
    String? description,
    bool? isEnabled,
    int? minPoints,
    int? maxPoints,
    String? rules,
    int? gamesBeforeAd,
    DateTime? lastUpdated,
  }) {
    return MiniGameModel(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      isEnabled: isEnabled ?? this.isEnabled,
      minPoints: minPoints ?? this.minPoints,
      maxPoints: maxPoints ?? this.maxPoints,
      rules: rules ?? this.rules,
      gamesBeforeAd: gamesBeforeAd ?? this.gamesBeforeAd,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class SupportTicket {
  final String id;
  final String userId;
  final String userName;
  final String userEmail;
  final String subject;
  final String message;
  final String status; // 'open', 'in_progress', 'resolved', 'closed'
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final String? adminResponse;

  SupportTicket({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.subject,
    required this.message,
    this.status = 'open',
    required this.createdAt,
    this.resolvedAt,
    this.adminResponse,
  });

  factory SupportTicket.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return SupportTicket(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userEmail: data['userEmail'] ?? '',
      subject: data['subject'] ?? '',
      message: data['message'] ?? '',
      status: data['status'] ?? 'open',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      resolvedAt: (data['resolvedAt'] as Timestamp?)?.toDate(),
      adminResponse: data['adminResponse'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'subject': subject,
      'message': message,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'resolvedAt': resolvedAt != null ? Timestamp.fromDate(resolvedAt!) : null,
      'adminResponse': adminResponse,
    };
  }
}
