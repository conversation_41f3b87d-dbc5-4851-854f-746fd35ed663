import 'package:cloud_firestore/cloud_firestore.dart';

// App Settings Model for Admin Control
class AppSettings {
  final String id;
  final double pointsToRupeesRate;
  final double pointsToDiamondsRate;
  final int dailyLoginBasePoints;
  final int spinCooldownHours;
  final int maxDailyChests;
  final bool maintenanceMode;
  final String maintenanceMessage;
  final Map<String, dynamic> gameSettings;
  final Map<String, dynamic> adSettings;
  final DateTime lastUpdated;
  final String updatedBy;

  AppSettings({
    required this.id,
    required this.pointsToRupeesRate,
    required this.pointsToDiamondsRate,
    required this.dailyLoginBasePoints,
    required this.spinCooldownHours,
    required this.maxDailyChests,
    this.maintenanceMode = false,
    this.maintenanceMessage = '',
    this.gameSettings = const {},
    this.adSettings = const {},
    required this.lastUpdated,
    required this.updatedBy,
  });

  factory AppSettings.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return AppSettings(
      id: doc.id,
      pointsToRupeesRate: (data['pointsToRupeesRate'] ?? 100.0).toDouble(),
      pointsToDiamondsRate: (data['pointsToDiamondsRate'] ?? 50.0).toDouble(),
      dailyLoginBasePoints: data['dailyLoginBasePoints'] ?? 50,
      spinCooldownHours: data['spinCooldownHours'] ?? 24,
      maxDailyChests: data['maxDailyChests'] ?? 5,
      maintenanceMode: data['maintenanceMode'] ?? false,
      maintenanceMessage: data['maintenanceMessage'] ?? '',
      gameSettings: Map<String, dynamic>.from(data['gameSettings'] ?? {}),
      adSettings: Map<String, dynamic>.from(data['adSettings'] ?? {}),
      lastUpdated:
          (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedBy: data['updatedBy'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'pointsToRupeesRate': pointsToRupeesRate,
      'pointsToDiamondsRate': pointsToDiamondsRate,
      'dailyLoginBasePoints': dailyLoginBasePoints,
      'spinCooldownHours': spinCooldownHours,
      'maxDailyChests': maxDailyChests,
      'maintenanceMode': maintenanceMode,
      'maintenanceMessage': maintenanceMessage,
      'gameSettings': gameSettings,
      'adSettings': adSettings,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'updatedBy': updatedBy,
    };
  }

  AppSettings copyWith({
    double? pointsToRupeesRate,
    double? pointsToDiamondsRate,
    int? dailyLoginBasePoints,
    int? spinCooldownHours,
    int? maxDailyChests,
    bool? maintenanceMode,
    String? maintenanceMessage,
    Map<String, dynamic>? gameSettings,
    Map<String, dynamic>? adSettings,
    DateTime? lastUpdated,
    String? updatedBy,
  }) {
    return AppSettings(
      id: id,
      pointsToRupeesRate: pointsToRupeesRate ?? this.pointsToRupeesRate,
      pointsToDiamondsRate: pointsToDiamondsRate ?? this.pointsToDiamondsRate,
      dailyLoginBasePoints: dailyLoginBasePoints ?? this.dailyLoginBasePoints,
      spinCooldownHours: spinCooldownHours ?? this.spinCooldownHours,
      maxDailyChests: maxDailyChests ?? this.maxDailyChests,
      maintenanceMode: maintenanceMode ?? this.maintenanceMode,
      maintenanceMessage: maintenanceMessage ?? this.maintenanceMessage,
      gameSettings: gameSettings ?? this.gameSettings,
      adSettings: adSettings ?? this.adSettings,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}

// Admin Action Log Model
class AdminActionLog {
  final String id;
  final String adminId;
  final String adminEmail;
  final String action;
  final String targetType; // 'user', 'settings', 'redemption', 'system'
  final String? targetId;
  final Map<String, dynamic> oldValues;
  final Map<String, dynamic> newValues;
  final DateTime timestamp;
  final String description;

  AdminActionLog({
    required this.id,
    required this.adminId,
    required this.adminEmail,
    required this.action,
    required this.targetType,
    this.targetId,
    this.oldValues = const {},
    this.newValues = const {},
    required this.timestamp,
    required this.description,
  });

  factory AdminActionLog.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return AdminActionLog(
      id: doc.id,
      adminId: data['adminId'] ?? '',
      adminEmail: data['adminEmail'] ?? '',
      action: data['action'] ?? '',
      targetType: data['targetType'] ?? '',
      targetId: data['targetId'],
      oldValues: Map<String, dynamic>.from(data['oldValues'] ?? {}),
      newValues: Map<String, dynamic>.from(data['newValues'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      description: data['description'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'adminId': adminId,
      'adminEmail': adminEmail,
      'action': action,
      'targetType': targetType,
      'targetId': targetId,
      'oldValues': oldValues,
      'newValues': newValues,
      'timestamp': Timestamp.fromDate(timestamp),
      'description': description,
    };
  }
}

// Quiz Question Model for Admin
class AdminQuizQuestion {
  final String id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String category;
  final String difficulty;
  final DateTime createdAt;
  final DateTime? updatedAt;

  AdminQuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.category,
    required this.difficulty,
    required this.createdAt,
    this.updatedAt,
  });

  factory AdminQuizQuestion.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminQuizQuestion(
      id: doc.id,
      question: data['question'] ?? '',
      options: List<String>.from(data['options'] ?? []),
      correctAnswer: data['correctAnswer'] ?? '',
      category: data['category'] ?? '',
      difficulty: data['difficulty'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'category': category,
      'difficulty': difficulty,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  AdminQuizQuestion copyWith({
    String? id,
    String? question,
    List<String>? options,
    String? correctAnswer,
    String? category,
    String? difficulty,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminQuizQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Redemption Request Model for Admin
class AdminRedemptionRequest {
  final String id;
  final String userId;
  final String userEmail;
  final String userName;
  final int diamondCount;
  final String freeFireUID;
  final String status; // pending, approved, rejected
  final DateTime requestDate;
  final DateTime? processedDate;
  final String? adminNotes;

  AdminRedemptionRequest({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.userName,
    required this.diamondCount,
    required this.freeFireUID,
    required this.status,
    required this.requestDate,
    this.processedDate,
    this.adminNotes,
  });

  factory AdminRedemptionRequest.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminRedemptionRequest(
      id: doc.id,
      userId: data['userId'] ?? '',
      userEmail: data['userEmail'] ?? '',
      userName: data['userName'] ?? '',
      diamondCount: data['diamondCount'] ?? 0,
      freeFireUID: data['freeFireUID'] ?? '',
      status: data['status'] ?? 'pending',
      requestDate:
          (data['requestDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      processedDate: (data['processedDate'] as Timestamp?)?.toDate(),
      adminNotes: data['adminNotes'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'diamondCount': diamondCount,
      'freeFireUID': freeFireUID,
      'status': status,
      'requestDate': Timestamp.fromDate(requestDate),
      'processedDate':
          processedDate != null ? Timestamp.fromDate(processedDate!) : null,
      'adminNotes': adminNotes,
    };
  }
}

// Tips Model for Admin
class AdminTip {
  final String id;
  final String title;
  final String content;
  final String category;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;

  AdminTip({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
  });

  factory AdminTip.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminTip(
      id: doc.id,
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      category: data['category'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      isActive: data['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'content': content,
      'category': category,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isActive': isActive,
    };
  }
}

// Analytics Model for Admin Dashboard
class AdminAnalytics {
  final int totalUsers;
  final int dailyActiveUsers;
  final int weeklyActiveUsers;
  final int totalSpins;
  final int totalQuizzes;
  final int totalRedemptions;
  final int pendingRedemptions;
  final int totalPoints;
  final DateTime lastUpdated;

  AdminAnalytics({
    required this.totalUsers,
    required this.dailyActiveUsers,
    required this.weeklyActiveUsers,
    required this.totalSpins,
    required this.totalQuizzes,
    required this.totalRedemptions,
    required this.pendingRedemptions,
    required this.totalPoints,
    required this.lastUpdated,
  });

  factory AdminAnalytics.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminAnalytics(
      totalUsers: data['totalUsers'] ?? 0,
      dailyActiveUsers: data['dailyActiveUsers'] ?? 0,
      weeklyActiveUsers: data['weeklyActiveUsers'] ?? 0,
      totalSpins: data['totalSpins'] ?? 0,
      totalQuizzes: data['totalQuizzes'] ?? 0,
      totalRedemptions: data['totalRedemptions'] ?? 0,
      pendingRedemptions: data['pendingRedemptions'] ?? 0,
      totalPoints: data['totalPoints'] ?? 0,
      lastUpdated:
          (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'totalUsers': totalUsers,
      'dailyActiveUsers': dailyActiveUsers,
      'weeklyActiveUsers': weeklyActiveUsers,
      'totalSpins': totalSpins,
      'totalQuizzes': totalQuizzes,
      'totalRedemptions': totalRedemptions,
      'pendingRedemptions': pendingRedemptions,
      'totalPoints': totalPoints,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }
}

// User Management Model for Admin
class AdminUserInfo {
  final String uid;
  final String name;
  final String email;
  final int points;
  final DateTime lastLogin;
  final int dailyStreak;
  final String referralCode;
  final bool isBanned;
  final DateTime createdAt;
  final int totalSpins;
  final int totalQuizzes;

  AdminUserInfo({
    required this.uid,
    required this.name,
    required this.email,
    required this.points,
    required this.lastLogin,
    required this.dailyStreak,
    required this.referralCode,
    this.isBanned = false,
    required this.createdAt,
    this.totalSpins = 0,
    this.totalQuizzes = 0,
  });

  factory AdminUserInfo.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminUserInfo(
      uid: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      points: data['points'] ?? 0,
      lastLogin: (data['lastLogin'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dailyStreak: data['dailyStreak'] ?? 0,
      referralCode: data['referralCode'] ?? '',
      isBanned: data['isBanned'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      totalSpins: data['totalSpins'] ?? 0,
      totalQuizzes: data['totalQuizzes'] ?? 0,
    );
  }
}

// Notification Model for Admin
class AdminNotification {
  final String id;
  final String title;
  final String message;
  final DateTime scheduledTime;
  final bool isSent;
  final DateTime? sentTime;
  final int recipientCount;

  AdminNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.scheduledTime,
    this.isSent = false,
    this.sentTime,
    this.recipientCount = 0,
  });

  factory AdminNotification.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminNotification(
      id: doc.id,
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      scheduledTime:
          (data['scheduledTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isSent: data['isSent'] ?? false,
      sentTime: (data['sentTime'] as Timestamp?)?.toDate(),
      recipientCount: data['recipientCount'] ?? 0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'message': message,
      'scheduledTime': Timestamp.fromDate(scheduledTime),
      'isSent': isSent,
      'sentTime': sentTime != null ? Timestamp.fromDate(sentTime!) : null,
      'recipientCount': recipientCount,
    };
  }
}
