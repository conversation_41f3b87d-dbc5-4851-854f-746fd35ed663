import 'package:cloud_firestore/cloud_firestore.dart';

// Quiz Question Model for Admin
class AdminQuizQuestion {
  final String id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String category;
  final String difficulty;
  final DateTime createdAt;
  final DateTime? updatedAt;

  AdminQuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.category,
    required this.difficulty,
    required this.createdAt,
    this.updatedAt,
  });

  factory AdminQuizQuestion.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminQuizQuestion(
      id: doc.id,
      question: data['question'] ?? '',
      options: List<String>.from(data['options'] ?? []),
      correctAnswer: data['correctAnswer'] ?? '',
      category: data['category'] ?? '',
      difficulty: data['difficulty'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'category': category,
      'difficulty': difficulty,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  AdminQuizQuestion copyWith({
    String? id,
    String? question,
    List<String>? options,
    String? correctAnswer,
    String? category,
    String? difficulty,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminQuizQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Redemption Request Model for Admin
class AdminRedemptionRequest {
  final String id;
  final String userId;
  final String userEmail;
  final String userName;
  final int diamondCount;
  final String freeFireUID;
  final String status; // pending, approved, rejected
  final DateTime requestDate;
  final DateTime? processedDate;
  final String? adminNotes;

  AdminRedemptionRequest({
    required this.id,
    required this.userId,
    required this.userEmail,
    required this.userName,
    required this.diamondCount,
    required this.freeFireUID,
    required this.status,
    required this.requestDate,
    this.processedDate,
    this.adminNotes,
  });

  factory AdminRedemptionRequest.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminRedemptionRequest(
      id: doc.id,
      userId: data['userId'] ?? '',
      userEmail: data['userEmail'] ?? '',
      userName: data['userName'] ?? '',
      diamondCount: data['diamondCount'] ?? 0,
      freeFireUID: data['freeFireUID'] ?? '',
      status: data['status'] ?? 'pending',
      requestDate: (data['requestDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      processedDate: (data['processedDate'] as Timestamp?)?.toDate(),
      adminNotes: data['adminNotes'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'diamondCount': diamondCount,
      'freeFireUID': freeFireUID,
      'status': status,
      'requestDate': Timestamp.fromDate(requestDate),
      'processedDate': processedDate != null ? Timestamp.fromDate(processedDate!) : null,
      'adminNotes': adminNotes,
    };
  }
}

// Tips Model for Admin
class AdminTip {
  final String id;
  final String title;
  final String content;
  final String category;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;

  AdminTip({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
  });

  factory AdminTip.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminTip(
      id: doc.id,
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      category: data['category'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      isActive: data['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'content': content,
      'category': category,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isActive': isActive,
    };
  }
}

// Analytics Model for Admin Dashboard
class AdminAnalytics {
  final int totalUsers;
  final int dailyActiveUsers;
  final int weeklyActiveUsers;
  final int totalSpins;
  final int totalQuizzes;
  final int totalRedemptions;
  final int pendingRedemptions;
  final int totalPoints;
  final DateTime lastUpdated;

  AdminAnalytics({
    required this.totalUsers,
    required this.dailyActiveUsers,
    required this.weeklyActiveUsers,
    required this.totalSpins,
    required this.totalQuizzes,
    required this.totalRedemptions,
    required this.pendingRedemptions,
    required this.totalPoints,
    required this.lastUpdated,
  });

  factory AdminAnalytics.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminAnalytics(
      totalUsers: data['totalUsers'] ?? 0,
      dailyActiveUsers: data['dailyActiveUsers'] ?? 0,
      weeklyActiveUsers: data['weeklyActiveUsers'] ?? 0,
      totalSpins: data['totalSpins'] ?? 0,
      totalQuizzes: data['totalQuizzes'] ?? 0,
      totalRedemptions: data['totalRedemptions'] ?? 0,
      pendingRedemptions: data['pendingRedemptions'] ?? 0,
      totalPoints: data['totalPoints'] ?? 0,
      lastUpdated: (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'totalUsers': totalUsers,
      'dailyActiveUsers': dailyActiveUsers,
      'weeklyActiveUsers': weeklyActiveUsers,
      'totalSpins': totalSpins,
      'totalQuizzes': totalQuizzes,
      'totalRedemptions': totalRedemptions,
      'pendingRedemptions': pendingRedemptions,
      'totalPoints': totalPoints,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }
}

// User Management Model for Admin
class AdminUserInfo {
  final String uid;
  final String name;
  final String email;
  final int points;
  final DateTime lastLogin;
  final int dailyStreak;
  final String referralCode;
  final bool isBanned;
  final DateTime createdAt;
  final int totalSpins;
  final int totalQuizzes;

  AdminUserInfo({
    required this.uid,
    required this.name,
    required this.email,
    required this.points,
    required this.lastLogin,
    required this.dailyStreak,
    required this.referralCode,
    this.isBanned = false,
    required this.createdAt,
    this.totalSpins = 0,
    this.totalQuizzes = 0,
  });

  factory AdminUserInfo.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminUserInfo(
      uid: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      points: data['points'] ?? 0,
      lastLogin: (data['lastLogin'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dailyStreak: data['dailyStreak'] ?? 0,
      referralCode: data['referralCode'] ?? '',
      isBanned: data['isBanned'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      totalSpins: data['totalSpins'] ?? 0,
      totalQuizzes: data['totalQuizzes'] ?? 0,
    );
  }
}

// Notification Model for Admin
class AdminNotification {
  final String id;
  final String title;
  final String message;
  final DateTime scheduledTime;
  final bool isSent;
  final DateTime? sentTime;
  final int recipientCount;

  AdminNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.scheduledTime,
    this.isSent = false,
    this.sentTime,
    this.recipientCount = 0,
  });

  factory AdminNotification.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminNotification(
      id: doc.id,
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      scheduledTime: (data['scheduledTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isSent: data['isSent'] ?? false,
      sentTime: (data['sentTime'] as Timestamp?)?.toDate(),
      recipientCount: data['recipientCount'] ?? 0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'message': message,
      'scheduledTime': Timestamp.fromDate(scheduledTime),
      'isSent': isSent,
      'sentTime': sentTime != null ? Timestamp.fromDate(sentTime!) : null,
      'recipientCount': recipientCount,
    };
  }
}
