import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String name;
  final String email;
  final int points;
  final int diamonds;
  final Avatar avatar;
  final DateTime lastLogin;
  final int dailyStreak;
  final String referralCode;
  final DateTime createdAt;
  final int totalSpins;
  final int totalGamesPlayed;
  final int totalQuizAnswered;
  final List<String> unlockedAvatarItems;
  final bool isActive;
  final String status; // 'active', 'banned', 'suspended'

  UserModel({
    required this.uid,
    required this.name,
    required this.email,
    required this.points,
    this.diamonds = 0,
    required this.avatar,
    required this.lastLogin,
    required this.dailyStreak,
    required this.referralCode,
    required this.createdAt,
    this.totalSpins = 0,
    this.totalGamesPlayed = 0,
    this.totalQuizAnswered = 0,
    this.unlockedAvatarItems = const [],
    this.isActive = true,
    this.status = 'active',
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return UserModel(
      uid: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      points: data['points'] ?? 0,
      diamonds: data['diamonds'] ?? 0,
      avatar: Avatar.fromMap(data['avatar'] ?? {}),
      lastLogin: (data['lastLogin'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dailyStreak: data['dailyStreak'] ?? 0,
      referralCode: data['referralCode'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      totalSpins: data['totalSpins'] ?? 0,
      totalGamesPlayed: data['totalGamesPlayed'] ?? 0,
      totalQuizAnswered: data['totalQuizAnswered'] ?? 0,
      unlockedAvatarItems: List<String>.from(data['unlockedAvatarItems'] ?? []),
      isActive: data['isActive'] ?? true,
      status: data['status'] ?? 'active',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'email': email,
      'points': points,
      'diamonds': diamonds,
      'avatar': avatar.toMap(),
      'lastLogin': Timestamp.fromDate(lastLogin),
      'dailyStreak': dailyStreak,
      'referralCode': referralCode,
      'createdAt': Timestamp.fromDate(createdAt),
      'totalSpins': totalSpins,
      'totalGamesPlayed': totalGamesPlayed,
      'totalQuizAnswered': totalQuizAnswered,
      'unlockedAvatarItems': unlockedAvatarItems,
      'isActive': isActive,
      'status': status,
    };
  }

  UserModel copyWith({
    String? name,
    String? email,
    int? points,
    int? diamonds,
    Avatar? avatar,
    DateTime? lastLogin,
    int? dailyStreak,
    String? referralCode,
    int? totalSpins,
    int? totalGamesPlayed,
    int? totalQuizAnswered,
    List<String>? unlockedAvatarItems,
    bool? isActive,
    String? status,
  }) {
    return UserModel(
      uid: uid,
      name: name ?? this.name,
      email: email ?? this.email,
      points: points ?? this.points,
      diamonds: diamonds ?? this.diamonds,
      avatar: avatar ?? this.avatar,
      lastLogin: lastLogin ?? this.lastLogin,
      dailyStreak: dailyStreak ?? this.dailyStreak,
      referralCode: referralCode ?? this.referralCode,
      createdAt: createdAt,
      totalSpins: totalSpins ?? this.totalSpins,
      totalGamesPlayed: totalGamesPlayed ?? this.totalGamesPlayed,
      totalQuizAnswered: totalQuizAnswered ?? this.totalQuizAnswered,
      unlockedAvatarItems: unlockedAvatarItems ?? this.unlockedAvatarItems,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
    );
  }
}

class Avatar {
  final String hair;
  final String clothing;
  final String background;

  Avatar({
    required this.hair,
    required this.clothing,
    required this.background,
  });

  factory Avatar.fromMap(Map<String, dynamic> map) {
    return Avatar(
      hair: map['hair'] ?? 'short_black',
      clothing: map['clothing'] ?? 'casual_tshirt',
      background: map['background'] ?? 'city',
    );
  }

  Map<String, dynamic> toMap() {
    return {'hair': hair, 'clothing': clothing, 'background': background};
  }

  Avatar copyWith({String? hair, String? clothing, String? background}) {
    return Avatar(
      hair: hair ?? this.hair,
      clothing: clothing ?? this.clothing,
      background: background ?? this.background,
    );
  }
}

class LeaderboardEntry {
  final String uid;
  final String name;
  final int points;
  final Avatar avatar;
  final int rank;

  LeaderboardEntry({
    required this.uid,
    required this.name,
    required this.points,
    required this.avatar,
    required this.rank,
  });

  factory LeaderboardEntry.fromMap(Map<String, dynamic> map, int rank) {
    return LeaderboardEntry(
      uid: map['uid'] ?? '',
      name: map['name'] ?? '',
      points: map['points'] ?? 0,
      avatar: Avatar.fromMap(map['avatar'] ?? {}),
      rank: rank,
    );
  }
}
