import 'package:cloud_firestore/cloud_firestore.dart';

class QuizQuestion {
  final String id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String category;
  final int difficulty; // 1-3 (easy, medium, hard)
  final String explanation;
  final int? correctAnswerIndex; // For daily quiz (0-3)
  final int points; // Points for this question

  QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.category,
    required this.difficulty,
    this.explanation = '',
    this.correctAnswerIndex,
    this.points = 20,
  });

  factory QuizQuestion.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return QuizQuestion(
      id: doc.id,
      question: data['question'] ?? '',
      options: List<String>.from(data['options'] ?? []),
      correctAnswer: data['correctAnswer'] ?? '',
      category: data['category'] ?? '',
      difficulty: data['difficulty'] ?? 1,
      explanation: data['explanation'] ?? '',
      correctAnswerIndex: data['correctAnswerIndex'],
      points: data['points'] ?? 20,
    );
  }

  factory QuizQuestion.fromMap(Map<String, dynamic> map) {
    // Handle both int and string for correctAnswer
    dynamic correctAnswerValue = map['correctAnswer'];
    String correctAnswerString = '';
    int? correctAnswerIndex;

    if (correctAnswerValue is int) {
      // If it's an index (0-3), use it directly
      correctAnswerIndex = correctAnswerValue;
      final options = List<String>.from(map['options'] ?? []);
      if (correctAnswerIndex >= 0 && correctAnswerIndex < options.length) {
        correctAnswerString = options[correctAnswerIndex];
      }
    } else if (correctAnswerValue is String) {
      // If it's a string, find the index
      correctAnswerString = correctAnswerValue;
      final options = List<String>.from(map['options'] ?? []);
      correctAnswerIndex = options.indexOf(correctAnswerString);
      if (correctAnswerIndex == -1) correctAnswerIndex = 0;
    }

    return QuizQuestion(
      id: map['id'] ?? '',
      question: map['question'] ?? '',
      options: List<String>.from(map['options'] ?? []),
      correctAnswer: correctAnswerString,
      category: map['category'] ?? 'General',
      difficulty: map['difficulty'] ?? 1,
      explanation: map['explanation'] ?? '',
      correctAnswerIndex: correctAnswerIndex,
      points: map['points'] ?? 20,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'category': category,
      'difficulty': difficulty,
      'explanation': explanation,
      'correctAnswerIndex': correctAnswerIndex,
      'points': points,
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctAnswer': correctAnswerIndex ?? correctAnswer,
      'explanation': explanation,
      'points': points,
    };
  }
}

class QuizLevel {
  final int level;
  final List<QuizQuestion> questions;
  final int pointsReward;
  final bool isCompleted;

  QuizLevel({
    required this.level,
    required this.questions,
    required this.pointsReward,
    this.isCompleted = false,
  });
}

class QuizSession {
  final String sessionId;
  final String userId;
  final int level;
  final List<QuizAnswer> answers;
  final DateTime startTime;
  final DateTime? endTime;
  final int score;
  final int pointsEarned;
  final bool isCompleted;

  QuizSession({
    required this.sessionId,
    required this.userId,
    required this.level,
    required this.answers,
    required this.startTime,
    this.endTime,
    required this.score,
    required this.pointsEarned,
    this.isCompleted = false,
  });

  factory QuizSession.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return QuizSession(
      sessionId: doc.id,
      userId: data['userId'] ?? '',
      level: data['level'] ?? 1,
      answers:
          (data['answers'] as List<dynamic>?)
              ?.map((answer) => QuizAnswer.fromMap(answer))
              .toList() ??
          [],
      startTime: (data['startTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endTime: (data['endTime'] as Timestamp?)?.toDate(),
      score: data['score'] ?? 0,
      pointsEarned: data['pointsEarned'] ?? 0,
      isCompleted: data['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'level': level,
      'answers': answers.map((answer) => answer.toMap()).toList(),
      'startTime': Timestamp.fromDate(startTime),
      'endTime': endTime != null ? Timestamp.fromDate(endTime!) : null,
      'score': score,
      'pointsEarned': pointsEarned,
      'isCompleted': isCompleted,
    };
  }
}

class QuizAnswer {
  final String questionId;
  final String selectedAnswer;
  final String correctAnswer;
  final bool isCorrect;
  final int timeSpent; // in seconds

  QuizAnswer({
    required this.questionId,
    required this.selectedAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.timeSpent,
  });

  factory QuizAnswer.fromMap(Map<String, dynamic> map) {
    return QuizAnswer(
      questionId: map['questionId'] ?? '',
      selectedAnswer: map['selectedAnswer'] ?? '',
      correctAnswer: map['correctAnswer'] ?? '',
      isCorrect: map['isCorrect'] ?? false,
      timeSpent: map['timeSpent'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'questionId': questionId,
      'selectedAnswer': selectedAnswer,
      'correctAnswer': correctAnswer,
      'isCorrect': isCorrect,
      'timeSpent': timeSpent,
    };
  }
}

// Daily Quiz Models
class DailyQuiz {
  final String date;
  final List<QuizQuestion> questions;
  final DateTime createdAt;
  final int totalQuestions;

  DailyQuiz({
    required this.date,
    required this.questions,
    required this.createdAt,
    required this.totalQuestions,
  });

  factory DailyQuiz.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return DailyQuiz(
      date: data['date'] ?? '',
      questions:
          (data['questions'] as List<dynamic>?)
              ?.map((q) => QuizQuestion.fromMap(q as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      totalQuestions: data['totalQuestions'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'questions': questions.map((q) => q.toMap()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'totalQuestions': totalQuestions,
    };
  }
}

class DailyQuizAttempt {
  final String id;
  final String userId;
  final String quizDate;
  final List<DailyQuizAnswer> answers;
  final int score;
  final int totalPoints;
  final DateTime completedAt;
  final Duration timeTaken;

  DailyQuizAttempt({
    required this.id,
    required this.userId,
    required this.quizDate,
    required this.answers,
    required this.score,
    required this.totalPoints,
    required this.completedAt,
    required this.timeTaken,
  });

  factory DailyQuizAttempt.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return DailyQuizAttempt(
      id: doc.id,
      userId: data['userId'] ?? '',
      quizDate: data['quizDate'] ?? '',
      answers:
          (data['answers'] as List<dynamic>?)
              ?.map((a) => DailyQuizAnswer.fromMap(a as Map<String, dynamic>))
              .toList() ??
          [],
      score: data['score'] ?? 0,
      totalPoints: data['totalPoints'] ?? 0,
      completedAt:
          (data['completedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      timeTaken: Duration(seconds: data['timeTakenSeconds'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'quizDate': quizDate,
      'answers': answers.map((a) => a.toMap()).toList(),
      'score': score,
      'totalPoints': totalPoints,
      'completedAt': Timestamp.fromDate(completedAt),
      'timeTakenSeconds': timeTaken.inSeconds,
    };
  }

  double get percentage => totalPoints > 0 ? (score / totalPoints) * 100 : 0;

  String get grade {
    final percent = percentage;
    if (percent >= 90) return 'A+';
    if (percent >= 80) return 'A';
    if (percent >= 70) return 'B';
    if (percent >= 60) return 'C';
    if (percent >= 50) return 'D';
    return 'F';
  }
}

class DailyQuizAnswer {
  final String questionId;
  final int selectedAnswer;
  final int correctAnswer;
  final bool isCorrect;
  final int pointsEarned;

  DailyQuizAnswer({
    required this.questionId,
    required this.selectedAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.pointsEarned,
  });

  factory DailyQuizAnswer.fromMap(Map<String, dynamic> map) {
    return DailyQuizAnswer(
      questionId: map['questionId'] ?? '',
      selectedAnswer: map['selectedAnswer'] ?? -1,
      correctAnswer: map['correctAnswer'] ?? 0,
      isCorrect: map['isCorrect'] ?? false,
      pointsEarned: map['pointsEarned'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'questionId': questionId,
      'selectedAnswer': selectedAnswer,
      'correctAnswer': correctAnswer,
      'isCorrect': isCorrect,
      'pointsEarned': pointsEarned,
    };
  }
}

enum QuizStatus { notStarted, inProgress, completed, expired }

class DailyQuizState {
  final QuizStatus status;
  final DailyQuiz? quiz;
  final DailyQuizAttempt? attempt;
  final int currentQuestionIndex;
  final List<DailyQuizAnswer> answers;
  final DateTime? startTime;

  DailyQuizState({
    required this.status,
    this.quiz,
    this.attempt,
    this.currentQuestionIndex = 0,
    this.answers = const [],
    this.startTime,
  });

  DailyQuizState copyWith({
    QuizStatus? status,
    DailyQuiz? quiz,
    DailyQuizAttempt? attempt,
    int? currentQuestionIndex,
    List<DailyQuizAnswer>? answers,
    DateTime? startTime,
  }) {
    return DailyQuizState(
      status: status ?? this.status,
      quiz: quiz ?? this.quiz,
      attempt: attempt ?? this.attempt,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      answers: answers ?? this.answers,
      startTime: startTime ?? this.startTime,
    );
  }

  bool get isCompleted => status == QuizStatus.completed;
  bool get canStart => status == QuizStatus.notStarted;
  bool get isInProgress => status == QuizStatus.inProgress;

  int get totalQuestions => quiz?.questions.length ?? 0;
  int get answeredQuestions => answers.length;
  int get remainingQuestions => totalQuestions - answeredQuestions;

  double get progress =>
      totalQuestions > 0 ? answeredQuestions / totalQuestions : 0;
}

// Sample Free Fire quiz questions data
class QuizData {
  static List<Map<String, dynamic>> sampleQuestions = [
    {
      'question': 'Which weapon has the highest damage in Free Fire?',
      'options': ['AWM', 'M82B', 'Kar98k', 'SVD'],
      'correctAnswer': 'M82B',
      'category': 'Weapons',
      'difficulty': 2,
      'explanation':
          'M82B is a sniper rifle with the highest damage output in Free Fire.',
    },
    {
      'question': 'What is the maximum number of players in a Classic match?',
      'options': ['50', '52', '48', '60'],
      'correctAnswer': '50',
      'category': 'Game Modes',
      'difficulty': 1,
      'explanation': 'Classic Battle Royale mode supports up to 50 players.',
    },
    {
      'question': 'Which character has the ability "Camouflage"?',
      'options': ['Kelly', 'Hayato', 'Moco', 'Wukong'],
      'correctAnswer': 'Wukong',
      'category': 'Characters',
      'difficulty': 2,
      'explanation':
          'Wukong\'s ability is Camouflage, which transforms him into a bush.',
    },
    {
      'question': 'What does the Gloo Wall do?',
      'options': [
        'Heals players',
        'Provides cover',
        'Increases speed',
        'Deals damage',
      ],
      'correctAnswer': 'Provides cover',
      'category': 'Items',
      'difficulty': 1,
      'explanation':
          'Gloo Wall creates a temporary wall that provides cover from enemy fire.',
    },
    {
      'question': 'Which map was the first in Free Fire?',
      'options': ['Bermuda', 'Purgatory', 'Kalahari', 'Alpine'],
      'correctAnswer': 'Bermuda',
      'category': 'Maps',
      'difficulty': 2,
      'explanation':
          'Bermuda was the original map when Free Fire was first released.',
    },
  ];
}
