import 'package:cloud_firestore/cloud_firestore.dart';

class QuizQuestion {
  final String id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String category;
  final int difficulty; // 1-3 (easy, medium, hard)
  final String explanation;

  QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.category,
    required this.difficulty,
    this.explanation = '',
  });

  factory QuizQuestion.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return QuizQuestion(
      id: doc.id,
      question: data['question'] ?? '',
      options: List<String>.from(data['options'] ?? []),
      correctAnswer: data['correctAnswer'] ?? '',
      category: data['category'] ?? '',
      difficulty: data['difficulty'] ?? 1,
      explanation: data['explanation'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'category': category,
      'difficulty': difficulty,
      'explanation': explanation,
    };
  }
}

class QuizLevel {
  final int level;
  final List<QuizQuestion> questions;
  final int pointsReward;
  final bool isCompleted;

  QuizLevel({
    required this.level,
    required this.questions,
    required this.pointsReward,
    this.isCompleted = false,
  });
}

class QuizSession {
  final String sessionId;
  final String userId;
  final int level;
  final List<QuizAnswer> answers;
  final DateTime startTime;
  final DateTime? endTime;
  final int score;
  final int pointsEarned;
  final bool isCompleted;

  QuizSession({
    required this.sessionId,
    required this.userId,
    required this.level,
    required this.answers,
    required this.startTime,
    this.endTime,
    required this.score,
    required this.pointsEarned,
    this.isCompleted = false,
  });

  factory QuizSession.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return QuizSession(
      sessionId: doc.id,
      userId: data['userId'] ?? '',
      level: data['level'] ?? 1,
      answers: (data['answers'] as List<dynamic>?)
          ?.map((answer) => QuizAnswer.fromMap(answer))
          .toList() ?? [],
      startTime: (data['startTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endTime: (data['endTime'] as Timestamp?)?.toDate(),
      score: data['score'] ?? 0,
      pointsEarned: data['pointsEarned'] ?? 0,
      isCompleted: data['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'level': level,
      'answers': answers.map((answer) => answer.toMap()).toList(),
      'startTime': Timestamp.fromDate(startTime),
      'endTime': endTime != null ? Timestamp.fromDate(endTime!) : null,
      'score': score,
      'pointsEarned': pointsEarned,
      'isCompleted': isCompleted,
    };
  }
}

class QuizAnswer {
  final String questionId;
  final String selectedAnswer;
  final String correctAnswer;
  final bool isCorrect;
  final int timeSpent; // in seconds

  QuizAnswer({
    required this.questionId,
    required this.selectedAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.timeSpent,
  });

  factory QuizAnswer.fromMap(Map<String, dynamic> map) {
    return QuizAnswer(
      questionId: map['questionId'] ?? '',
      selectedAnswer: map['selectedAnswer'] ?? '',
      correctAnswer: map['correctAnswer'] ?? '',
      isCorrect: map['isCorrect'] ?? false,
      timeSpent: map['timeSpent'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'questionId': questionId,
      'selectedAnswer': selectedAnswer,
      'correctAnswer': correctAnswer,
      'isCorrect': isCorrect,
      'timeSpent': timeSpent,
    };
  }
}

// Sample Free Fire quiz questions data
class QuizData {
  static List<Map<String, dynamic>> sampleQuestions = [
    {
      'question': 'Which weapon has the highest damage in Free Fire?',
      'options': ['AWM', 'M82B', 'Kar98k', 'SVD'],
      'correctAnswer': 'M82B',
      'category': 'Weapons',
      'difficulty': 2,
      'explanation': 'M82B is a sniper rifle with the highest damage output in Free Fire.',
    },
    {
      'question': 'What is the maximum number of players in a Classic match?',
      'options': ['50', '52', '48', '60'],
      'correctAnswer': '50',
      'category': 'Game Modes',
      'difficulty': 1,
      'explanation': 'Classic Battle Royale mode supports up to 50 players.',
    },
    {
      'question': 'Which character has the ability "Camouflage"?',
      'options': ['Kelly', 'Hayato', 'Moco', 'Wukong'],
      'correctAnswer': 'Wukong',
      'category': 'Characters',
      'difficulty': 2,
      'explanation': 'Wukong\'s ability is Camouflage, which transforms him into a bush.',
    },
    {
      'question': 'What does the Gloo Wall do?',
      'options': ['Heals players', 'Provides cover', 'Increases speed', 'Deals damage'],
      'correctAnswer': 'Provides cover',
      'category': 'Items',
      'difficulty': 1,
      'explanation': 'Gloo Wall creates a temporary wall that provides cover from enemy fire.',
    },
    {
      'question': 'Which map was the first in Free Fire?',
      'options': ['Bermuda', 'Purgatory', 'Kalahari', 'Alpine'],
      'correctAnswer': 'Bermuda',
      'category': 'Maps',
      'difficulty': 2,
      'explanation': 'Bermuda was the original map when Free Fire was first released.',
    },
  ];
}
