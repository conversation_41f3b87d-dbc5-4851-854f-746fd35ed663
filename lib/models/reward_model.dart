import 'package:cloud_firestore/cloud_firestore.dart';

enum RewardType {
  spin,
  game,
  quiz,
  dailyLogin,
  referral,
  secretChest,
  bonus,
}

class Reward {
  final String id;
  final String userId;
  final RewardType type;
  final int points;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  Reward({
    required this.id,
    required this.userId,
    required this.type,
    required this.points,
    required this.timestamp,
    this.metadata = const {},
  });

  factory Reward.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return Reward(
      id: doc.id,
      userId: data['userId'] ?? '',
      type: RewardType.values.firstWhere(
        (e) => e.toString() == 'RewardType.${data['type']}',
        orElse: () => RewardType.bonus,
      ),
      points: data['points'] ?? 0,
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'type': type.toString().split('.').last,
      'points': points,
      'timestamp': Timestamp.fromDate(timestamp),
      'metadata': metadata,
    };
  }
}

class SpinReward {
  final int points;
  final String message;
  final bool isJackpot;

  SpinReward({
    required this.points,
    required this.message,
    this.isJackpot = false,
  });
}

class DailyLoginReward {
  final int day;
  final int points;
  final String bonus;
  final bool isClaimed;

  DailyLoginReward({
    required this.day,
    required this.points,
    this.bonus = '',
    this.isClaimed = false,
  });
}

class GameReward {
  final String gameType;
  final int score;
  final int points;
  final bool isNewRecord;

  GameReward({
    required this.gameType,
    required this.score,
    required this.points,
    this.isNewRecord = false,
  });
}

class Redemption {
  final String id;
  final String userId;
  final String freeFireUID;
  final int pointsSpent;
  final int diamondsRequested;
  final DateTime requestDate;
  final RedemptionStatus status;
  final String? adminNotes;
  final DateTime? processedDate;

  Redemption({
    required this.id,
    required this.userId,
    required this.freeFireUID,
    required this.pointsSpent,
    required this.diamondsRequested,
    required this.requestDate,
    required this.status,
    this.adminNotes,
    this.processedDate,
  });

  factory Redemption.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return Redemption(
      id: doc.id,
      userId: data['userId'] ?? '',
      freeFireUID: data['freeFireUID'] ?? '',
      pointsSpent: data['pointsSpent'] ?? 0,
      diamondsRequested: data['diamondsRequested'] ?? 0,
      requestDate: (data['requestDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: RedemptionStatus.values.firstWhere(
        (e) => e.toString() == 'RedemptionStatus.${data['status']}',
        orElse: () => RedemptionStatus.pending,
      ),
      adminNotes: data['adminNotes'],
      processedDate: (data['processedDate'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'freeFireUID': freeFireUID,
      'pointsSpent': pointsSpent,
      'diamondsRequested': diamondsRequested,
      'requestDate': Timestamp.fromDate(requestDate),
      'status': status.toString().split('.').last,
      'adminNotes': adminNotes,
      'processedDate': processedDate != null ? Timestamp.fromDate(processedDate!) : null,
    };
  }
}

enum RedemptionStatus {
  pending,
  processing,
  completed,
  rejected,
}

class SecretChestReward {
  final RewardType type;
  final int value;
  final String description;
  final String rarity; // common, rare, epic, legendary

  SecretChestReward({
    required this.type,
    required this.value,
    required this.description,
    required this.rarity,
  });

  static List<SecretChestReward> getRandomRewards() {
    final rewards = [
      SecretChestReward(
        type: RewardType.bonus,
        value: 50,
        description: '50 Bonus Points',
        rarity: 'common',
      ),
      SecretChestReward(
        type: RewardType.bonus,
        value: 100,
        description: '100 Bonus Points',
        rarity: 'common',
      ),
      SecretChestReward(
        type: RewardType.bonus,
        value: 200,
        description: '200 Bonus Points',
        rarity: 'rare',
      ),
      SecretChestReward(
        type: RewardType.bonus,
        value: 500,
        description: '500 Bonus Points',
        rarity: 'epic',
      ),
      SecretChestReward(
        type: RewardType.bonus,
        value: 1000,
        description: '1000 Bonus Points - JACKPOT!',
        rarity: 'legendary',
      ),
    ];
    
    // Weighted random selection
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    if (random < 50) return [rewards[0]]; // 50% common
    if (random < 80) return [rewards[1]]; // 30% common
    if (random < 95) return [rewards[2]]; // 15% rare
    if (random < 99) return [rewards[3]]; // 4% epic
    return [rewards[4]]; // 1% legendary
  }
}
