import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'providers/auth_provider.dart';
import 'providers/user_provider.dart';
import 'providers/admin_provider.dart';
import 'services/admob_service.dart';
import 'utils/app_theme.dart';
import 'utils/constants.dart';
import 'screens/splash_screen.dart';
import 'screens/admin/admin_login_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('Firebase initialized successfully');

    // Initialize AdMob
    await AdMobService.initialize();
    debugPrint('AdMob initialized successfully');
  } catch (e) {
    // Log error but continue app execution
    debugPrint('Initialization error: $e');
    // Show a warning but don't crash the app
    debugPrint('App will continue with limited functionality');
  }

  runApp(const FireZoneApp());
}

class FireZoneApp extends StatelessWidget {
  const FireZoneApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => AdminProvider()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.darkTheme,
        home: const SplashScreen(),
        debugShowCheckedModeBanner: false,
        routes: {'/admin-login': (context) => const AdminLoginScreen()},
      ),
    );
  }
}
