import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../utils/app_theme.dart';
import '../widgets/custom_button.dart';

class DailyRewardBanner extends StatelessWidget {
  const DailyRewardBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final canClaim = userProvider.canClaimDailyReward;
        final rewardPoints = userProvider.dailyRewardPoints;
        final user = userProvider.user;
        final streak = user?.dailyStreak ?? 0;

        if (!canClaim) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Icon
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.card_giftcard,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Daily Reward Available!',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Day $streak • $rewardPoints Points',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color.fromARGB(255, 255, 255, 255).withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Claim Button
              CustomButton(
                text: 'Claim',
                te
                onPressed: userProvider.isLoading ? null : () async {
                  await userProvider.claimDailyReward();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Claimed $rewardPoints points!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
                isLoading: userProvider.isLoading,
                backgroundColor: Colors.white,
                textColor: Colors.green,
                width: 80,
                height: 36,
                borderRadius: 18,
              ),
            ],
          ),
        );
      },
    );
  }
}

class DailyRewardDialog extends StatefulWidget {
  final int day;
  final int points;
  final VoidCallback? onClaim;
  final VoidCallback? onWatchAd;

  const DailyRewardDialog({
    Key? key,
    required this.day,
    required this.points,
    this.onClaim,
    this.onWatchAd,
  }) : super(key: key);

  @override
  State<DailyRewardDialog> createState() => _DailyRewardDialogState();
}

class _DailyRewardDialogState extends State<DailyRewardDialog>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppTheme.cardDark,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppTheme.accentGold,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.accentGold.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Animated Gift Icon
              RotationTransition(
                turns: _rotationAnimation,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: AppTheme.goldGradient,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.accentGold.withOpacity(0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.card_giftcard,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Title
              Text(
                'Daily Reward',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: AppTheme.accentGold,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Day and Points
              Text(
                'Day ${widget.day}',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              
              const SizedBox(height: 16),
              
              // Points Display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.stars,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${widget.points} Points',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: 'Claim',
                      onPressed: () {
                        Navigator.of(context).pop();
                        widget.onClaim?.call();
                      },
                      backgroundColor: AppTheme.primaryRed,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomButton(
                      text: 'Watch Ad\n+50% Bonus',
                      onPressed: () {
                        Navigator.of(context).pop();
                        widget.onWatchAd?.call();
                      },
                      backgroundColor: AppTheme.accentGold,
                      textColor: Colors.black,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DailyRewardCalendar extends StatelessWidget {
  final int currentDay;
  final List<bool> claimedDays;

  const DailyRewardCalendar({
    Key? key,
    required this.currentDay,
    required this.claimedDays,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Daily Rewards Calendar',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 30, // 30 days
            itemBuilder: (context, index) {
              final day = index + 1;
              final isClaimed = index < claimedDays.length && claimedDays[index];
              final isToday = day == currentDay;
              final isAvailable = day <= currentDay;
              
              return Container(
                decoration: BoxDecoration(
                  color: isClaimed
                      ? AppTheme.successGreen
                      : isToday
                          ? AppTheme.accentGold
                          : isAvailable
                              ? AppTheme.primaryRed.withOpacity(0.3)
                              : AppTheme.textGray.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: isToday
                      ? Border.all(color: AppTheme.accentGold, width: 2)
                      : null,
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isClaimed)
                        const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      else
                        Text(
                          '$day',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
