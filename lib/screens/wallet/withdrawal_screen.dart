import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../services/redemption_service.dart';
import '../../models/redemption_model.dart';

class WithdrawalScreen extends StatefulWidget {
  final String type; // 'rupees' or 'diamonds'

  const WithdrawalScreen({super.key, required this.type});

  @override
  State<WithdrawalScreen> createState() => _WithdrawalScreenState();
}

class _WithdrawalScreenState extends State<WithdrawalScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pointsController = TextEditingController();
  bool _isLoading = false;
  double _conversionRate = 100.0; // Default: 100 points = 1 rupee/diamond

  @override
  void initState() {
    super.initState();
    _loadConversionRate();
  }

  @override
  void dispose() {
    _pointsController.dispose();
    super.dispose();
  }

  void _loadConversionRate() {
    // Load conversion rate from app settings
    if (widget.type == 'rupees') {
      _conversionRate = 100.0; // 100 points = 1 rupee
    } else {
      _conversionRate = 50.0; // 50 points = 1 diamond
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Withdraw ${widget.type == 'rupees' ? 'Rupees' : 'Diamonds'}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final user = userProvider.user;

            if (user == null) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.accentGold,
                  ),
                ),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Balance Card
                    _buildBalanceCard(user),
                    const SizedBox(height: 24),

                    // Withdrawal Form
                    _buildWithdrawalForm(user),
                    const SizedBox(height: 24),

                    // Payment Details
                    _buildPaymentDetails(user),
                    const SizedBox(height: 24),

                    // Conversion Info
                    _buildConversionInfo(),
                    const SizedBox(height: 30),

                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      child: CustomButton(
                        text:
                            _isLoading
                                ? 'Processing...'
                                : 'Submit Withdrawal Request',
                        icon: _isLoading ? null : Icons.send,
                        onPressed: _isLoading ? null : _submitWithdrawal,
                        backgroundColor: AppTheme.accentGold,
                        textColor: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBalanceCard(user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: AppTheme.accentGold,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Current Balance',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildBalanceItem(
                  'Points',
                  '${user.points}',
                  Icons.stars,
                  AppTheme.accentGold,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBalanceItem(
                  widget.type == 'rupees' ? 'Available ₹' : 'Available 💎',
                  widget.type == 'rupees'
                      ? '₹${(user.points / _conversionRate).toStringAsFixed(2)}'
                      : '${(user.points / _conversionRate).toInt()}',
                  widget.type == 'rupees'
                      ? Icons.currency_rupee
                      : Icons.diamond,
                  widget.type == 'rupees' ? AppTheme.successGreen : Colors.cyan,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWithdrawalForm(user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.redeem, color: AppTheme.accentGold, size: 24),
              const SizedBox(width: 12),
              Text(
                'Withdrawal Amount',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _pointsController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              labelText: 'Points to Withdraw',
              hintText: 'Enter points (minimum ${_conversionRate.toInt()})',
              prefixIcon: const Icon(Icons.stars, color: AppTheme.accentGold),
              labelStyle: const TextStyle(color: AppTheme.accentGold),
              hintStyle: const TextStyle(color: Colors.grey),
              filled: true,
              fillColor: AppTheme.cardDark.withValues(alpha: 0.5),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.accentGold),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.accentGold),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppTheme.accentGold,
                  width: 2,
                ),
              ),
            ),
            validator: (value) => _validatePoints(value, user.points),
            onChanged:
                (value) =>
                    setState(() {}), // Trigger rebuild for conversion display
          ),
          const SizedBox(height: 16),
          if (_pointsController.text.isNotEmpty) _buildConversionDisplay(),
        ],
      ),
    );
  }

  Widget _buildConversionDisplay() {
    final points = int.tryParse(_pointsController.text) ?? 0;
    final convertedAmount = points / _conversionRate;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.accentGold.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.accentGold.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$points Points',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Icon(Icons.arrow_forward, color: AppTheme.accentGold),
          Text(
            widget.type == 'rupees'
                ? '₹${convertedAmount.toStringAsFixed(2)}'
                : '${convertedAmount.toInt()} 💎',
            style: TextStyle(
              color:
                  widget.type == 'rupees' ? AppTheme.successGreen : Colors.cyan,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails(user) {
    final paymentMethod = widget.type == 'rupees' ? user.upiId : user.gameId;
    final paymentLabel =
        widget.type == 'rupees' ? 'UPI ID' : 'Free Fire Game ID';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.payment, color: AppTheme.accentGold, size: 24),
              const SizedBox(width: 12),
              Text(
                'Payment Details',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                widget.type == 'rupees'
                    ? Icons.account_balance_wallet
                    : Icons.videogame_asset,
                color:
                    widget.type == 'rupees'
                        ? AppTheme.successGreen
                        : Colors.cyan,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      paymentLabel,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      paymentMethod ?? 'Not set',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            paymentMethod != null
                                ? AppTheme.successGreen
                                : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (user.phoneNumber != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.phone, color: Colors.blue, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Phone Number',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        user.phoneNumber!,
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(color: Colors.blue),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildConversionInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                'Important Information',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoItem(
            '• Minimum withdrawal: ${_conversionRate.toInt()} points',
          ),
          _buildInfoItem('• Processing time: 24-48 hours'),
          _buildInfoItem(
            widget.type == 'rupees'
                ? '• UPI payments are usually instant'
                : '• Diamond transfers take 1-2 hours',
          ),
          _buildInfoItem(
            '• Withdrawal requests cannot be cancelled once submitted',
          ),
          _buildInfoItem('• Contact support if you face any issues'),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: Colors.grey[300]),
      ),
    );
  }

  String? _validatePoints(String? value, int userPoints) {
    if (value == null || value.isEmpty) {
      return 'Please enter points to withdraw';
    }

    final points = int.tryParse(value);
    if (points == null) {
      return 'Please enter a valid number';
    }

    if (points < _conversionRate) {
      return 'Minimum withdrawal is ${_conversionRate.toInt()} points';
    }

    if (points > userPoints) {
      return 'Insufficient points (Available: $userPoints)';
    }

    return null;
  }

  Future<void> _submitWithdrawal() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final user = Provider.of<UserProvider>(context, listen: false).user;
    if (user == null) return;

    // Check payment method
    final paymentMethod = widget.type == 'rupees' ? user.upiId : user.gameId;
    if (paymentMethod == null || paymentMethod.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please setup your ${widget.type == 'rupees' ? 'UPI ID' : 'Game ID'} first',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final points = int.parse(_pointsController.text);
      final amount = points / _conversionRate;

      final request = RedemptionRequest(
        id: '',
        userId: user.uid,
        userName: user.name,
        userEmail: user.email,
        type:
            widget.type == 'rupees'
                ? RedemptionType.rupees
                : RedemptionType.diamonds,
        pointsUsed: points,
        amount: amount,
        requestDate: DateTime.now(),
        upiId: widget.type == 'rupees' ? user.upiId : null,
        gameId: widget.type == 'diamonds' ? user.gameId : null,
        phoneNumber: user.phoneNumber,
      );

      await RedemptionService.submitWithdrawalRequest(request);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Withdrawal request submitted successfully!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
