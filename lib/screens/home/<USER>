import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/feature_card.dart';
import '../../widgets/points_display.dart';
import '../../widgets/daily_reward_banner.dart';
import '../games/spin_wheel_screen.dart';
import '../games/mini_games_screen.dart';
import '../quiz/quiz_screen.dart';
import '../profile/profile_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const HomeTab(),
    const GamesTab(),
    const ProfileTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: _screens[_currentIndex],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppTheme.cardDark,
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryRed.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: AppTheme.primaryRed,
          unselectedItemColor: AppTheme.textGray,
          type: BottomNavigationBarType.fixed,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.games),
              label: 'Games',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person),
              label: 'Profile',
            ),
          ],
        ),
      ),
    );
  }
}

class HomeTab extends StatelessWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(context),
            
            const SizedBox(height: 20),
            
            // Points Display
            const PointsDisplay(),
            
            const SizedBox(height: 20),
            
            // Daily Reward Banner
            const DailyRewardBanner(),
            
            const SizedBox(height: 30),
            
            // Features Grid
            Text(
              'Earn Points',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            
            _buildFeaturesGrid(context),
            
            const SizedBox(height: 30),
            
            // Other Features
            Text(
              'More Features',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            
            _buildOtherFeatures(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.user;
        return Row(
          children: [
            // Avatar
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: AppTheme.primaryGradient,
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 30,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Welcome Text
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textGray,
                    ),
                  ),
                  Text(
                    user?.name ?? 'User',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
            ),
            
            // Notification Icon
            IconButton(
              onPressed: () {
                // TODO: Show notifications
              },
              icon: const Icon(
                Icons.notifications_outlined,
                color: AppTheme.textWhite,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFeaturesGrid(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        FeatureCard(
          title: 'Spin Wheel',
          subtitle: 'Spin every 4 hours',
          icon: Icons.casino,
          gradient: AppTheme.primaryGradient,
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const SpinWheelScreen()),
            );
          },
        ),
        FeatureCard(
          title: 'Mini Games',
          subtitle: 'Play & earn points',
          icon: Icons.games,
          gradient: AppTheme.goldGradient,
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const MiniGamesScreen()),
            );
          },
        ),
        FeatureCard(
          title: 'Quiz',
          subtitle: 'Free Fire trivia',
          icon: Icons.quiz,
          gradient: const LinearGradient(
            colors: [Colors.purple, Colors.deepPurple],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const QuizScreen()),
            );
          },
        ),
        FeatureCard(
          title: 'Secret Chest',
          subtitle: 'Watch ad & win',
          icon: Icons.card_giftcard,
          gradient: const LinearGradient(
            colors: [Colors.green, Colors.teal],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          onTap: () {
            // TODO: Open secret chest
          },
        ),
      ],
    );
  }

  Widget _buildOtherFeatures(BuildContext context) {
    return Column(
      children: [
        _buildFeatureRow(
          context,
          'Leaderboard',
          'See top players',
          Icons.leaderboard,
          () {
            // TODO: Navigate to leaderboard
          },
        ),
        const SizedBox(height: 12),
        _buildFeatureRow(
          context,
          'Tips & Tricks',
          'Free Fire guides',
          Icons.lightbulb,
          () {
            // TODO: Navigate to tips
          },
        ),
        const SizedBox(height: 12),
        _buildFeatureRow(
          context,
          'Referral',
          'Invite friends',
          Icons.share,
          () {
            // TODO: Navigate to referral
          },
        ),
        const SizedBox(height: 12),
        _buildFeatureRow(
          context,
          'Redeem Diamonds',
          'Exchange points',
          Icons.diamond,
          () {
            // TODO: Navigate to redemption
          },
        ),
      ],
    );
  }

  Widget _buildFeatureRow(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: AppTheme.cardDecoration,
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.primaryRed.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppTheme.primaryRed,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.textGray,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }
}

class GamesTab extends StatelessWidget {
  const GamesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Games Tab - Coming Soon'),
    );
  }
}

class ProfileTab extends StatelessWidget {
  const ProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfileScreen();
  }
}
