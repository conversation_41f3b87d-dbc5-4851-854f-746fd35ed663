import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/points_display.dart';
import '../../models/reward_model.dart';

class SpinWheelScreen extends StatefulWidget {
  const SpinWheelScreen({super.key});

  @override
  State<SpinWheelScreen> createState() => _SpinWheelScreenState();
}

class _SpinWheelScreenState extends State<SpinWheelScreen>
    with TickerProviderStateMixin {
  late AnimationController _spinController;
  late Animation<double> _spinAnimation;
  bool _isSpinning = false;
  int? _lastReward;

  final List<int> _wheelValues = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
  final List<Color> _wheelColors = [
    Colors.red,
    Colors.orange,
    Colors.yellow,
    Colors.green,
    Colors.blue,
    Colors.indigo,
    Colors.purple,
    Colors.pink,
    Colors.teal,
    Colors.amber,
  ];

  @override
  void initState() {
    super.initState();
    _spinController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _spinAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _spinController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _spinController.dispose();
    super.dispose();
  }

  Future<void> _spin() async {
    if (_isSpinning) return;

    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (!userProvider.canSpin) {
      _showCooldownDialog();
      return;
    }

    setState(() {
      _isSpinning = true;
    });

    try {
      final reward = await userProvider.performSpin();

      // Animate the wheel
      await _spinController.forward();

      setState(() {
        _lastReward = reward.points;
      });

      // Show reward dialog
      if (mounted) {
        _showRewardDialog(reward.points, reward.message, reward.isJackpot);
      }

      // Show ad after spin
      _showRewardedAd();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString()), backgroundColor: Colors.red),
        );
      }
    } finally {
      setState(() {
        _isSpinning = false;
      });
      _spinController.reset();
    }
  }

  void _showCooldownDialog() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final remaining = userProvider.spinCooldownRemaining;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('Spin Cooldown'),
            content: Text(
              'You can spin again in ${remaining.inHours}h ${remaining.inMinutes % 60}m',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showRewardDialog(int points, String message, bool isJackpot) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient:
                    isJackpot
                        ? AppTheme.goldGradient
                        : AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: (isJackpot
                            ? AppTheme.accentGold
                            : AppTheme.primaryRed)
                        .withOpacity(0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isJackpot ? Icons.emoji_events : Icons.stars,
                    size: 60,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    message,
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '+$points Points',
                    style: Theme.of(context).textTheme.displaySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  CustomButton(
                    text: 'Awesome!',
                    onPressed: () => Navigator.of(context).pop(),
                    backgroundColor: Colors.white,
                    textColor: isJackpot ? AppTheme.darkGold : AppTheme.darkRed,
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showRewardedAd() {
    AdMobService.instance.showRewardedAd(
      onRewarded: (bonusPoints) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        userProvider.addPoints(
          bonusPoints,
          RewardType.bonus,
          metadata: {'source': 'spin_wheel_ad', 'originalReward': _lastReward},
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Bonus! +$bonusPoints points from ad!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      onError: (error) {
        debugPrint('Ad error: $error');
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Spin Wheel'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Points Display
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: PointsDisplay(),
              ),

              const SizedBox(height: 40),

              // Spin Wheel
              Expanded(
                child: Center(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Wheel
                      AnimatedBuilder(
                        animation: _spinAnimation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _spinAnimation.value * 4 * pi,
                            child: Container(
                              width: 300,
                              height: 300,
                              child: CustomPaint(
                                painter: WheelPainter(
                                  values: _wheelValues,
                                  colors: _wheelColors,
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      // Center Circle
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: AppTheme.cardDark,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppTheme.primaryRed,
                            width: 3,
                          ),
                        ),
                        child: const Icon(
                          Icons.local_fire_department,
                          color: AppTheme.primaryRed,
                          size: 30,
                        ),
                      ),

                      // Pointer
                      Positioned(
                        top: 20,
                        child: Container(
                          width: 0,
                          height: 0,
                          decoration: const BoxDecoration(
                            border: Border(
                              left: BorderSide(
                                width: 15,
                                color: Colors.transparent,
                              ),
                              right: BorderSide(
                                width: 15,
                                color: Colors.transparent,
                              ),
                              bottom: BorderSide(
                                width: 30,
                                color: AppTheme.accentGold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Spin Button
              Padding(
                padding: const EdgeInsets.all(24),
                child: Consumer<UserProvider>(
                  builder: (context, userProvider, child) {
                    final canSpin = userProvider.canSpin;
                    final cooldown = userProvider.spinCooldownRemaining;

                    return Column(
                      children: [
                        if (!canSpin)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              'Next spin in ${cooldown.inHours}h ${cooldown.inMinutes % 60}m',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(color: AppTheme.textGray),
                            ),
                          ),

                        CustomButton(
                          text: _isSpinning ? 'Spinning...' : 'SPIN',
                          onPressed: canSpin && !_isSpinning ? _spin : null,
                          isLoading: _isSpinning,
                          width: double.infinity,
                          height: 60,
                        ),

                        const SizedBox(height: 16),

                        Text(
                          'Spin every ${AppConstants.spinCooldownHours} hours to earn ${AppConstants.minSpinPoints}-${AppConstants.maxSpinPoints} points!',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.textGray),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class WheelPainter extends CustomPainter {
  final List<int> values;
  final List<Color> colors;

  WheelPainter({required this.values, required this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final paint = Paint()..style = PaintingStyle.fill;

    final sectionAngle = 2 * pi / values.length;

    for (int i = 0; i < values.length; i++) {
      paint.color = colors[i % colors.length];

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        i * sectionAngle,
        sectionAngle,
        true,
        paint,
      );

      // Draw text
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${values[i]}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      final angle = i * sectionAngle + sectionAngle / 2;
      final textRadius = radius * 0.7;
      final textOffset = Offset(
        center.dx + textRadius * cos(angle) - textPainter.width / 2,
        center.dy + textRadius * sin(angle) - textPainter.height / 2,
      );

      textPainter.paint(canvas, textOffset);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
