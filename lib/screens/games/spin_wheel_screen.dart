import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/points_display.dart';
import '../../models/reward_model.dart';

// Wheel segment data class
class WheelSegment {
  final int points;
  final Color color;
  final String label;

  WheelSegment({
    required this.points,
    required this.color,
    required this.label,
  });
}

class SpinWheelScreen extends StatefulWidget {
  const SpinWheelScreen({super.key});

  @override
  State<SpinWheelScreen> createState() => _SpinWheelScreenState();
}

class _SpinWheelScreenState extends State<SpinWheelScreen>
    with TickerProviderStateMixin {
  late AnimationController _spinController;
  late Animation<double> _spinAnimation;
  bool _isSpinning = false;
  int _wonPoints = 0;

  // Simple 5-segment wheel
  final List<WheelSegment> _segments = [
    WheelSegment(points: 10, color: const Color(0xFFE74C3C), label: '10'),
    WheelSegment(points: 25, color: const Color(0xFF3498DB), label: '25'),
    WheelSegment(points: 50, color: const Color(0xFF2ECC71), label: '50'),
    WheelSegment(points: 100, color: const Color(0xFFF39C12), label: '100'),
    WheelSegment(points: 5, color: const Color(0xFF9B59B6), label: '5'),
  ];

  @override
  void initState() {
    super.initState();
    _spinController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _spinAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _spinController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _spinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Spin Wheel',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final user = userProvider.user;
            if (user == null) {
              return const Center(child: CircularProgressIndicator());
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Points Display
                  const PointsDisplay(),
                  const SizedBox(height: 30),

                  // Wheel Container
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: AppTheme.cardDecoration,
                    child: Column(
                      children: [
                        Text(
                          'Spin to Win Points!',
                          style: Theme.of(
                            context,
                          ).textTheme.headlineSmall?.copyWith(
                            color: AppTheme.accentGold,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 30),

                        // Wheel
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            // Spinning Wheel
                            AnimatedBuilder(
                              animation: _spinAnimation,
                              builder: (context, child) {
                                return Transform.rotate(
                                  angle: _spinAnimation.value * 8 * pi,
                                  child: SizedBox(
                                    width: 280,
                                    height: 280,
                                    child: CustomPaint(
                                      painter: SimpleWheelPainter(_segments),
                                    ),
                                  ),
                                );
                              },
                            ),

                            // Center Circle
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: AppTheme.accentGold,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.stars,
                                color: Colors.black,
                                size: 30,
                              ),
                            ),

                            // Pointer
                            Positioned(
                              top: 10,
                              child: Container(
                                width: 0,
                                height: 0,
                                decoration: const BoxDecoration(
                                  border: Border(
                                    left: BorderSide(
                                      width: 15,
                                      color: Colors.transparent,
                                    ),
                                    right: BorderSide(
                                      width: 15,
                                      color: Colors.transparent,
                                    ),
                                    bottom: BorderSide(
                                      width: 25,
                                      color: AppTheme.accentGold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 40),

                        // Spin Button
                        SizedBox(
                          width: double.infinity,
                          child: CustomButton(
                            text: _isSpinning ? 'Spinning...' : 'SPIN WHEEL',
                            icon: _isSpinning ? null : Icons.refresh,
                            onPressed: _isSpinning ? null : _spin,
                            backgroundColor: AppTheme.accentGold,
                            textColor: Colors.black,
                            height: 60,
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Instructions
                        Text(
                          'Tap the wheel to spin and win points!\nYou can spin once every 24 hours.',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[400]),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Prize List
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: AppTheme.cardDecoration,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Possible Rewards',
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ...List.generate(_segments.length, (index) {
                          final segment = _segments[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    color: segment.color,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '${segment.points} Points',
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(color: Colors.white),
                                ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _spin() async {
    if (_isSpinning) return;

    setState(() => _isSpinning = true);

    try {
      // Determine which segment wins (random)
      final random = Random();
      final winningIndex = random.nextInt(_segments.length);
      final winningSegment = _segments[winningIndex];

      // Animate the wheel
      await _spinController.forward();

      // Award points
      if (mounted) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        await userProvider.addPoints(
          winningSegment.points,
          RewardType.spin,
          metadata: {
            'game': 'spin_wheel',
            'segment': winningSegment.label,
            'spinTime': DateTime.now().toIso8601String(),
          },
        );
      }

      setState(() {
        _wonPoints = winningSegment.points;
      });

      // Show success dialog
      if (mounted) {
        _showWinDialog(winningSegment.points);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSpinning = false);
        _spinController.reset();
      }
    }
  }

  void _showWinDialog(int points) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Dialog(
            backgroundColor: AppTheme.cardDark,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppTheme.accentGold,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.accentGold.withValues(alpha: 0.4),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.stars,
                      color: Colors.black,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Congratulations!',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppTheme.accentGold,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'You won $points points!',
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(color: Colors.white),
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: CustomButton(
                      text: 'Awesome!',
                      onPressed: () => Navigator.pop(context),
                      backgroundColor: AppTheme.accentGold,
                      textColor: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}

// Simple wheel painter for 5 segments
class SimpleWheelPainter extends CustomPainter {
  final List<WheelSegment> segments;

  SimpleWheelPainter(this.segments);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final paint = Paint()..style = PaintingStyle.fill;

    final sectionAngle = 2 * pi / segments.length;

    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      paint.color = segment.color;

      // Draw segment
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        i * sectionAngle - pi / 2, // Start from top
        sectionAngle,
        true,
        paint,
      );

      // Draw border
      paint
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        i * sectionAngle - pi / 2,
        sectionAngle,
        true,
        paint,
      );

      // Draw text
      final textPainter = TextPainter(
        text: TextSpan(
          text: segment.label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(offset: Offset(1, 1), blurRadius: 2, color: Colors.black),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      final angle = i * sectionAngle + sectionAngle / 2 - pi / 2;
      final textRadius = radius * 0.7;
      final textOffset = Offset(
        center.dx + textRadius * cos(angle) - textPainter.width / 2,
        center.dy + textRadius * sin(angle) - textPainter.height / 2,
      );

      paint.style = PaintingStyle.fill;
      textPainter.paint(canvas, textOffset);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
