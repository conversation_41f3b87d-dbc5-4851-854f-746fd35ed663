import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/reward_model.dart';
import '../../widgets/banner_ad_widget.dart';
import '../../services/navigation_service.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class SecretChestScreen extends StatefulWidget {
  const SecretChestScreen({super.key});

  @override
  State<SecretChestScreen> createState() => _SecretChestScreenState();
}

class _SecretChestScreenState extends State<SecretChestScreen>
    with TickerProviderStateMixin {
  late AnimationController _chestController;
  late AnimationController _sparkleController;
  late Animation<double> _chestAnimation;
  late Animation<double> _sparkleAnimation;

  bool canOpenChest = true;
  bool isOpening = false;
  DateTime? lastChestOpen;
  int chestsOpenedToday = 0;
  final int maxChestsPerDay = 5;

  BannerAd? bannerAd;
  bool isBannerAdReady = false;
  Random random = Random();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadBannerAd();
    _checkChestAvailability();
  }

  @override
  void dispose() {
    _chestController.dispose();
    _sparkleController.dispose();
    bannerAd?.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _chestController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _sparkleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    _chestAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _chestController, curve: Curves.elasticOut),
    );

    _sparkleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sparkleController, curve: Curves.easeInOut),
    );
  }

  void _loadBannerAd() {
    bannerAd = AdMobService.instance.createBannerAd();
    bannerAd!.load().then((_) {
      setState(() {
        isBannerAdReady = true;
      });
    });
  }

  void _checkChestAvailability() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final user = userProvider.user;

    if (user != null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final lastLogin = DateTime(
        user.lastLogin.year,
        user.lastLogin.month,
        user.lastLogin.day,
      );

      if (today.isAfter(lastLogin)) {
        // New day, reset chest count
        setState(() {
          chestsOpenedToday = 0;
          canOpenChest = true;
        });
      } else {
        // Same day, check how many chests opened
        // This would normally be stored in user data
        setState(() {
          canOpenChest = chestsOpenedToday < maxChestsPerDay;
        });
      }
    }
  }

  void _openChest() {
    if (!canOpenChest || isOpening) return;

    setState(() {
      isOpening = true;
    });

    // Show interstitial ad before opening chest
    AdMobService.instance.showInterstitialAd(
      onAdClosed: () {
        _chestController.forward().then((_) {
          _showChestReward();
        });
      },
      onError: (error) {
        // If ad fails, continue with chest opening
        _chestController.forward().then((_) {
          _showChestReward();
        });
      },
    );
  }

  void _showChestReward() {
    // Generate random reward
    final rewardType = random.nextInt(3);
    int points = 0;
    String rewardText = '';
    IconData rewardIcon = Icons.star;
    Color rewardColor = AppTheme.accentGold;

    switch (rewardType) {
      case 0: // Points
        points = random.nextInt(31) + 20; // 20-50 points
        rewardText = '$points Points';
        rewardIcon = Icons.stars;
        rewardColor = AppTheme.accentGold;
        break;
      case 1: // Bonus Points
        points = random.nextInt(21) + 30; // 30-50 points
        rewardText = '$points Bonus Points';
        rewardIcon = Icons.auto_awesome;
        rewardColor = AppTheme.successGreen;
        break;
      case 2: // Lucky Points
        points = random.nextInt(41) + 10; // 10-50 points
        rewardText = '$points Lucky Points';
        rewardIcon = Icons.casino;
        rewardColor = AppTheme.primaryRed;
        break;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: Row(
              children: [
                Icon(rewardIcon, color: rewardColor),
                const SizedBox(width: 8),
                const Text('🎁 Chest Reward!'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: rewardColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: rewardColor),
                  ),
                  child: Text(
                    rewardText,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: rewardColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Chests opened today: ${chestsOpenedToday + 1}/$maxChestsPerDay',
                ),
                const SizedBox(height: 16),
                const Text('Watch an ad for double rewards?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _awardPoints(points);
                  Navigator.of(context).pop();
                  _completeChestOpening();
                },
                child: const Text('Take Reward'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAd(points);
                },
                child: const Text('Double It!'),
              ),
            ],
          ),
    );
  }

  void _showRewardedAd(int basePoints) {
    AdMobService.instance.showRewardedAd(
      onRewarded: (bonusPoints) {
        _awardPoints(basePoints * 2); // Double the reward
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Amazing! You got ${basePoints * 2} points!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        _completeChestOpening();
      },
      onAdClosed: () {
        _awardPoints(basePoints);
        _completeChestOpening();
      },
      onError: (error) {
        _awardPoints(basePoints);
        _completeChestOpening();
      },
    );
  }

  void _awardPoints(int points) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.addPoints(
      points,
      RewardType.secretChest,
      metadata: {
        'source': 'secret_chest',
        'chest_number': chestsOpenedToday + 1,
      },
    );
  }

  void _completeChestOpening() {
    setState(() {
      chestsOpenedToday++;
      canOpenChest = chestsOpenedToday < maxChestsPerDay;
      isOpening = false;
      lastChestOpen = DateTime.now();
    });

    // Reset chest animation
    _chestController.reset();
  }

  String _getTimeUntilNextChest() {
    if (canOpenChest) return '';

    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final difference = tomorrow.difference(now);

    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;

    return 'Next chest in: ${hours}h ${minutes}m';
  }

  @override
  Widget build(BuildContext context) {
    return AdAwareWillPopScope(
      child: Scaffold(
        backgroundColor: AppTheme.backgroundBlack,
        appBar: AppBar(
          title: const Text('Secret Chest'),
          backgroundColor: AppTheme.backgroundBlack,
        ),
        body: Column(
          children: [
            // Banner Ad
            const BannerAdWidget(),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Title and Description
                  Text(
                    '🏴‍☠️ Secret Treasure Chest',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: AppTheme.accentGold,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 10),

                  Text(
                    'Open chests to discover hidden treasures!\n'
                    'Get points, bonuses, and special rewards.',
                    textAlign: TextAlign.center,
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: AppTheme.textGray),
                  ),

                  const SizedBox(height: 30),

                  // Chest Status
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.cardDark,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.accentGold.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Daily Chests: $chestsOpenedToday/$maxChestsPerDay',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        if (!canOpenChest &&
                            chestsOpenedToday >= maxChestsPerDay)
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              _getTimeUntilNextChest(),
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: AppTheme.textGray),
                            ),
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Animated Chest
                  AnimatedBuilder(
                    animation: _sparkleAnimation,
                    builder: (context, child) {
                      return Stack(
                        alignment: Alignment.center,
                        children: [
                          // Sparkle effects
                          if (canOpenChest) ..._buildSparkles(),

                          // Main chest
                          GestureDetector(
                            onTap: _openChest,
                            child: AnimatedBuilder(
                              animation: _chestAnimation,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: 1.0 + (_chestAnimation.value * 0.2),
                                  child: Container(
                                    width: 200,
                                    height: 200,
                                    decoration: BoxDecoration(
                                      gradient:
                                          canOpenChest
                                              ? RadialGradient(
                                                colors: [
                                                  AppTheme.accentGold,
                                                  AppTheme.accentGold
                                                      .withOpacity(0.7),
                                                ],
                                              )
                                              : RadialGradient(
                                                colors: [
                                                  AppTheme.textGray,
                                                  AppTheme.textGray.withOpacity(
                                                    0.7,
                                                  ),
                                                ],
                                              ),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color:
                                            canOpenChest
                                                ? AppTheme.accentGold
                                                : AppTheme.textGray,
                                        width: 4,
                                      ),
                                      boxShadow:
                                          canOpenChest
                                              ? [
                                                BoxShadow(
                                                  color: AppTheme.accentGold
                                                      .withOpacity(0.5),
                                                  blurRadius: 20,
                                                  spreadRadius: 5,
                                                ),
                                              ]
                                              : null,
                                    ),
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            isOpening
                                                ? Icons.lock_open
                                                : Icons.lock,
                                            size: 60,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(height: 10),
                                          Text(
                                            '🏴‍☠️',
                                            style: const TextStyle(
                                              fontSize: 40,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      );
                    },
                  ),

                  const SizedBox(height: 40),

                  // Action Button
                  if (canOpenChest && !isOpening)
                    CustomButton(
                      text: 'Open Chest',
                      icon: Icons.lock_open,
                      onPressed: _openChest,
                      backgroundColor: AppTheme.accentGold,
                      textColor: Colors.black,
                      width: 200,
                    )
                  else if (isOpening)
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.accentGold,
                      ),
                    )
                  else
                    CustomButton(
                      text: 'Come Back Tomorrow',
                      icon: Icons.schedule,
                      onPressed: null,
                      backgroundColor: AppTheme.textGray,
                      width: 200,
                    ),

                  const SizedBox(height: 30),

                  // Rewards Info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.cardDark.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Possible Rewards:',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildRewardInfo('⭐', '20-50\nPoints'),
                            _buildRewardInfo('💎', 'Bonus\nRewards'),
                            _buildRewardInfo('🍀', 'Lucky\nPrizes'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardInfo(String emoji, String text) {
    return Column(
      children: [
        Text(emoji, style: const TextStyle(fontSize: 30)),
        const SizedBox(height: 8),
        Text(
          text,
          textAlign: TextAlign.center,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppTheme.textGray),
        ),
      ],
    );
  }

  List<Widget> _buildSparkles() {
    return List.generate(8, (index) {
      final angle = (index * 45) * (3.14159 / 180); // Convert to radians
      final distance = 120 + (sin(_sparkleAnimation.value * 2 * 3.14159) * 20);

      return Positioned(
        left: 100 + cos(angle) * distance,
        top: 100 + sin(angle) * distance,
        child: Transform.rotate(
          angle: _sparkleAnimation.value * 2 * 3.14159,
          child: Icon(
            Icons.auto_awesome,
            color: AppTheme.accentGold.withOpacity(0.7),
            size: 20,
          ),
        ),
      );
    });
  }
}
