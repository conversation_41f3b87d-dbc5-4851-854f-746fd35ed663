import 'package:flutter/material.dart';
import '../../models/quiz_model.dart';
import '../../services/daily_quiz_service.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/points_display.dart';
import 'quiz_question_screen.dart';
import 'quiz_results_screen.dart';

class DailyQuizScreen extends StatefulWidget {
  const DailyQuizScreen({super.key});

  @override
  State<DailyQuizScreen> createState() => _DailyQuizScreenState();
}

class _DailyQuizScreenState extends State<DailyQuizScreen> {
  DailyQuizState? _quizState;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadQuizState();
  }

  Future<void> _loadQuizState() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final state = await DailyQuizService.getCurrentQuizState();

      if (mounted) {
        setState(() {
          _quizState = state;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _startQuiz() async {
    if (_quizState?.quiz == null) return;

    final result = await Navigator.push<DailyQuizAttempt>(
      context,
      MaterialPageRoute(
        builder: (context) => QuizQuestionScreen(quiz: _quizState!.quiz!),
      ),
    );

    if (result != null) {
      // Quiz completed, show results
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QuizResultsScreen(attempt: result),
          ),
        );
      }

      // Reload quiz state
      _loadQuizState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Daily Quiz',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              // Points Display
              const Padding(
                padding: EdgeInsets.all(16),
                child: PointsDisplay(),
              ),

              // Main Content
              Expanded(
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _error != null
                        ? _buildErrorWidget()
                        : _buildQuizContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Try Again',
              onPressed: _loadQuizState,
              backgroundColor: AppTheme.primaryRed,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizContent() {
    if (_quizState == null) {
      return const Center(child: Text('No quiz data available'));
    }

    switch (_quizState!.status) {
      case QuizStatus.notStarted:
        return _buildQuizAvailable();
      case QuizStatus.completed:
        return _buildQuizCompleted();
      case QuizStatus.expired:
        return _buildQuizExpired();
      case QuizStatus.inProgress:
        return _buildQuizInProgress();
    }
  }

  Widget _buildQuizAvailable() {
    final quiz = _quizState!.quiz!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Quiz Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: AppTheme.cardDecoration,
            child: Column(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppTheme.accentGold,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.accentGold.withValues(alpha: 0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: const Icon(Icons.quiz, color: Colors.black, size: 40),
                ),
                const SizedBox(height: 16),
                Text(
                  'Today\'s Free Fire Quiz',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppTheme.accentGold,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Test your Free Fire knowledge!',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(color: Colors.grey[400]),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Quiz Info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: AppTheme.cardDecoration,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quiz Details',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildInfoRow(
                  Icons.help_outline,
                  'Questions',
                  '${quiz.totalQuestions}',
                ),
                _buildInfoRow(Icons.timer, 'Time Limit', 'No limit'),
                _buildInfoRow(Icons.stars, 'Points per Question', '20'),
                _buildInfoRow(
                  Icons.emoji_events,
                  'Total Points',
                  '${quiz.totalQuestions * 20}',
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.accentGold.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.accentGold.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppTheme.accentGold,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'You can only take this quiz once per day. Make sure you\'re ready!',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.accentGold),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Start Button
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: 'START QUIZ',
              icon: Icons.play_arrow,
              onPressed: _startQuiz,
              backgroundColor: AppTheme.accentGold,
              textColor: Colors.black,
              height: 60,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.accentGold, size: 20),
          const SizedBox(width: 12),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
          ),
          const Spacer(),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizCompleted() {
    final attempt = _quizState!.attempt!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: AppTheme.cardDecoration,
            child: Column(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withValues(alpha: 0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 40),
                ),
                const SizedBox(height: 16),
                Text(
                  'Quiz Completed!',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You\'ve already completed today\'s quiz',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(color: Colors.grey[400]),
                ),
                const SizedBox(height: 20),
                Text(
                  'Your Score: ${attempt.score}/${attempt.totalPoints}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.accentGold,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Grade: ${attempt.grade}',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.white),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: 'VIEW RESULTS',
              icon: Icons.assessment,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => QuizResultsScreen(attempt: attempt),
                  ),
                );
              },
              backgroundColor: AppTheme.primaryRed,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Come back tomorrow for a new quiz!',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuizExpired() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.schedule, size: 64, color: Colors.orange[400]),
            const SizedBox(height: 16),
            Text(
              'No Quiz Available',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Today\'s quiz is not ready yet. Please check back later!',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Refresh',
              onPressed: _loadQuizState,
              backgroundColor: AppTheme.primaryRed,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizInProgress() {
    return const Center(child: Text('Quiz in progress...'));
  }
}
