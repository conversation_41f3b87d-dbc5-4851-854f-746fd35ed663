import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';
import '../../widgets/feature_card.dart';
import '../../services/admob_service.dart';
import 'memory_match_game.dart';
import 'weapon_collection_game.dart';
import 'color_match_game.dart';
import 'quick_math_game.dart';
import 'secret_chest_screen.dart';
import 'daily_quiz_screen.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class MiniGamesScreen extends StatefulWidget {
  const MiniGamesScreen({super.key});

  @override
  State<MiniGamesScreen> createState() => _MiniGamesScreenState();
}

class _MiniGamesScreenState extends State<MiniGamesScreen> {
  BannerAd? bannerAd;
  bool isBannerAdReady = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  @override
  void dispose() {
    bannerAd?.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    bannerAd = AdMobService.instance.createBannerAd();
    bannerAd!.load().then((_) {
      setState(() {
        isBannerAdReady = true;
      });
    });
  }

  void _showInterstitialAd(VoidCallback onComplete) {
    AdMobService.instance.showInterstitialAd(
      onAdClosed: onComplete,
      onError: (error) => onComplete(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mini Games'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Banner Ad
          if (isBannerAdReady && bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: bannerAd!.size.width.toDouble(),
              height: bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: bannerAd!),
            ),

          Expanded(
            child: Container(
              decoration: AppTheme.backgroundDecoration,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Daily Quiz Feature
                      FeatureCard(
                        title: '🧠 Daily Quiz',
                        subtitle: 'Test your Free Fire knowledge',
                        icon: Icons.quiz,
                        gradient: LinearGradient(
                          colors: [
                            Colors.deepPurple,
                            Colors.deepPurple.withValues(alpha: 0.7),
                          ],
                        ),
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => const DailyQuizScreen(),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // Secret Chest Feature
                      FeatureCard(
                        title: '🏴‍☠️ Secret Chest',
                        subtitle: 'Daily treasure rewards',
                        icon: Icons.lock,
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.accentGold,
                            AppTheme.accentGold.withValues(alpha: 0.7),
                          ],
                        ),
                        onTap: () {
                          _showInterstitialAd(() {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const SecretChestScreen(),
                              ),
                            );
                          });
                        },
                      ),

                      const SizedBox(height: 20),
                      Text(
                        'Choose a Game',
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                      const SizedBox(height: 20),

                      Expanded(
                        child: GridView.count(
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          childAspectRatio: 1.2,
                          children: [
                            FeatureCard(
                              title: 'Memory Match',
                              subtitle: 'Match the cards',
                              icon: Icons.memory,
                              gradient: const LinearGradient(
                                colors: [Colors.blue, Colors.indigo],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              onTap: () {
                                _showInterstitialAd(() {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (_) => const MemoryMatchGame(),
                                    ),
                                  );
                                });
                              },
                            ),
                            FeatureCard(
                              title: 'Weapon Collection',
                              subtitle: 'Collect Free Fire weapons',
                              icon: Icons.sports_esports,
                              gradient: const LinearGradient(
                                colors: [Colors.orange, Colors.deepOrange],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              onTap: () {
                                _showInterstitialAd(() {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder:
                                          (_) => const WeaponCollectionGame(),
                                    ),
                                  );
                                });
                              },
                            ),
                            FeatureCard(
                              title: 'Color Match',
                              subtitle: 'Match the colors',
                              icon: Icons.palette,
                              gradient: const LinearGradient(
                                colors: [Colors.purple, Colors.pink],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              onTap: () {
                                _showInterstitialAd(() {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (_) => const ColorMatchGame(),
                                    ),
                                  );
                                });
                              },
                            ),
                            FeatureCard(
                              title: 'Quick Math',
                              subtitle: 'Solve equations',
                              icon: Icons.calculate,
                              gradient: const LinearGradient(
                                colors: [Colors.teal, Colors.cyan],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              onTap: () {
                                _showInterstitialAd(() {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (_) => const QuickMathGame(),
                                    ),
                                  );
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: AppTheme.cardDecoration,
                        child: Column(
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.info_outline,
                                  color: AppTheme.accentGold,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Game Rules',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(color: AppTheme.accentGold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              '• Earn 20-50 points per game completed\n'
                              '• Watch ads after every 3 games for bonus points\n'
                              '• Higher scores earn more points\n'
                              '• New games added regularly!',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: AppTheme.textGray),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
