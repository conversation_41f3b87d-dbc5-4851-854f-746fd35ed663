import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';
import '../../widgets/feature_card.dart';

class MiniGamesScreen extends StatelessWidget {
  const MiniGamesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mini Games'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Choose a Game',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 20),
                
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.2,
                    children: [
                      FeatureCard(
                        title: 'Memory Match',
                        subtitle: 'Match the cards',
                        icon: Icons.memory,
                        gradient: const LinearGradient(
                          colors: [Colors.blue, Colors.indigo],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Coming Soon!')),
                          );
                        },
                      ),
                      FeatureCard(
                        title: 'Tap Target',
                        subtitle: 'Hit the targets',
                        icon: Icons.gps_fixed,
                        gradient: const LinearGradient(
                          colors: [Colors.orange, Colors.deepOrange],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Coming Soon!')),
                          );
                        },
                      ),
                      FeatureCard(
                        title: 'Color Match',
                        subtitle: 'Match the colors',
                        icon: Icons.palette,
                        gradient: const LinearGradient(
                          colors: [Colors.purple, Colors.pink],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Coming Soon!')),
                          );
                        },
                      ),
                      FeatureCard(
                        title: 'Quick Math',
                        subtitle: 'Solve equations',
                        icon: Icons.calculate,
                        gradient: const LinearGradient(
                          colors: [Colors.teal, Colors.cyan],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Coming Soon!')),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.info_outline,
                            color: AppTheme.accentGold,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Game Rules',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppTheme.accentGold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '• Earn 20-50 points per game completed\n'
                        '• Watch ads after every 3 games for bonus points\n'
                        '• Higher scores earn more points\n'
                        '• New games added regularly!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
