import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/reward_model.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class QuickMathGame extends StatefulWidget {
  const QuickMathGame({super.key});

  @override
  State<QuickMathGame> createState() => _QuickMathGameState();
}

class _QuickMathGameState extends State<QuickMathGame> {
  Timer? gameTimer;
  int timeLeft = 60;
  int score = 0;
  int level = 1;
  int correctAnswers = 0;
  int wrongAnswers = 0;
  bool gameStarted = false;
  bool gameEnded = false;

  String equation = '';
  int correctAnswer = 0;
  List<int> answerOptions = [];
  Random random = Random();
  BannerAd? bannerAd;
  bool isBannerAdReady = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    bannerAd?.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    bannerAd = AdMobService.instance.createBannerAd();
    bannerAd!.load().then((_) {
      setState(() {
        isBannerAdReady = true;
      });
    });
  }

  void _startGame() {
    setState(() {
      gameStarted = true;
      timeLeft = 60;
      score = 0;
      level = 1;
      correctAnswers = 0;
      wrongAnswers = 0;
    });

    _generateEquation();

    gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        timeLeft--;
      });

      if (timeLeft <= 0) {
        _endGame();
      }
    });
  }

  void _generateEquation() {
    int num1, num2, result;
    String operator;

    // Increase difficulty with level
    int maxNumber = 10 + (level * 5);

    switch (random.nextInt(4)) {
      case 0: // Addition
        num1 = random.nextInt(maxNumber) + 1;
        num2 = random.nextInt(maxNumber) + 1;
        result = num1 + num2;
        operator = '+';
        break;
      case 1: // Subtraction
        num1 = random.nextInt(maxNumber) + 10;
        num2 = random.nextInt(num1) + 1;
        result = num1 - num2;
        operator = '-';
        break;
      case 2: // Multiplication
        num1 = random.nextInt(min(12, maxNumber ~/ 2)) + 1;
        num2 = random.nextInt(min(12, maxNumber ~/ 2)) + 1;
        result = num1 * num2;
        operator = '×';
        break;
      case 3: // Division
        num2 = random.nextInt(min(12, maxNumber ~/ 2)) + 1;
        result = random.nextInt(min(20, maxNumber)) + 1;
        num1 = num2 * result;
        operator = '÷';
        break;
      default:
        num1 = 1;
        num2 = 1;
        result = 2;
        operator = '+';
    }

    setState(() {
      equation = '$num1 $operator $num2 = ?';
      correctAnswer = result;
      answerOptions = _generateAnswerOptions(result);
    });
  }

  List<int> _generateAnswerOptions(int correct) {
    List<int> options = [correct];

    while (options.length < 4) {
      int wrongAnswer;
      if (correct <= 10) {
        wrongAnswer = random.nextInt(20) + 1;
      } else if (correct <= 50) {
        wrongAnswer = correct + random.nextInt(21) - 10; // ±10
      } else {
        wrongAnswer = correct + random.nextInt(41) - 20; // ±20
      }

      if (wrongAnswer > 0 && !options.contains(wrongAnswer)) {
        options.add(wrongAnswer);
      }
    }

    options.shuffle();
    return options;
  }

  void _selectAnswer(int answer) {
    if (answer == correctAnswer) {
      setState(() {
        correctAnswers++;
        score += (10 + level * 2); // More points for higher levels

        // Level up every 8 correct answers
        if (correctAnswers % 8 == 0) {
          level++;
        }
      });

      _showFeedback(true);
    } else {
      setState(() {
        wrongAnswers++;
        score = (score - 3).clamp(0, double.infinity).toInt();
      });

      _showFeedback(false);
    }

    // Generate next equation after delay
    Timer(const Duration(milliseconds: 600), () {
      if (gameStarted && !gameEnded) {
        _generateEquation();
      }
    });
  }

  void _showFeedback(bool correct) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder:
          (context) => Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: correct ? AppTheme.successGreen : Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  correct ? '✓ Correct!' : '✗ Wrong!',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
    );

    overlay.insert(overlayEntry);
    Timer(const Duration(milliseconds: 500), () {
      overlayEntry.remove();
    });
  }

  void _endGame() {
    gameTimer?.cancel();
    setState(() {
      gameEnded = true;
    });

    int pointsEarned = _calculatePointsEarned();

    if (pointsEarned > 0) {
      _showGameCompleteDialog(pointsEarned);
    } else {
      _showGameOverDialog();
    }
  }

  int _calculatePointsEarned() {
    if (correctAnswers >= 30) {
      return AppConstants.maxGamePoints; // 50 points
    } else if (correctAnswers >= 25) {
      return 40;
    } else if (correctAnswers >= 20) {
      return 35;
    } else if (correctAnswers >= 15) {
      return 30;
    } else if (correctAnswers >= 10) {
      return AppConstants.minGamePoints; // 20 points
    }
    return 0;
  }

  void _showGameCompleteDialog(int pointsEarned) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('🧮 Math Genius!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Level Reached: $level'),
                Text('Correct: $correctAnswers'),
                Text('Wrong: $wrongAnswers'),
                Text(
                  'Accuracy: ${(correctAnswers / (correctAnswers + wrongAnswers) * 100).toStringAsFixed(1)}%',
                ),
                Text('Points Earned: $pointsEarned'),
                const SizedBox(height: 16),
                const Text('Watch an ad for bonus points?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _awardPoints(pointsEarned);
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Skip'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAd(pointsEarned);
                },
                child: const Text('Watch Ad'),
              ),
            ],
          ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('⏰ Time\'s Up!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Level: $level'),
                Text('Correct: $correctAnswers'),
                const Text('Keep practicing your math skills!'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _restartGame();
                },
                child: const Text('Play Again'),
              ),
            ],
          ),
    );
  }

  void _showRewardedAd(int basePoints) {
    AdMobService.instance.showRewardedAd(
      onRewarded: (bonusPoints) {
        _awardPoints(basePoints + bonusPoints);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Bonus! +$bonusPoints points from ad!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      },
      onAdClosed: () {
        Navigator.of(context).pop();
      },
      onError: (error) {
        _awardPoints(basePoints);
        Navigator.of(context).pop();
      },
    );
  }

  void _awardPoints(int points) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.addPoints(
      points,
      RewardType.game,
      metadata: {
        'game': 'quick_math',
        'score': score,
        'level': level,
        'correct_answers': correctAnswers,
        'accuracy': correctAnswers / (correctAnswers + wrongAnswers),
      },
    );
  }

  void _restartGame() {
    setState(() {
      gameStarted = false;
      gameEnded = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        title: const Text('Quick Math'),
        backgroundColor: AppTheme.backgroundBlack,
        actions: [
          if (gameStarted && !gameEnded)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Text(
                  '⏱️ $timeLeft',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Banner Ad
          if (isBannerAdReady && bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: bannerAd!.size.width.toDouble(),
              height: bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: bannerAd!),
            ),

          // Game Stats
          if (gameStarted)
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    'Score: $score',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Level: $level',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Correct: $correctAnswers',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),

          Expanded(
            child: !gameStarted ? _buildStartScreen() : _buildGameArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.calculate, size: 80, color: AppTheme.accentGold),
          const SizedBox(height: 20),
          Text('Quick Math', style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 10),
          const Text(
            'Solve math equations as fast as you can!\n'
            'Get 30+ correct to earn maximum points.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          CustomButton(
            text: 'Start Game',
            onPressed: _startGame,
            icon: Icons.play_arrow,
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Equation Display
          Container(
            padding: const EdgeInsets.all(30),
            decoration: BoxDecoration(
              color: AppTheme.cardDark,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: AppTheme.accentGold, width: 2),
            ),
            child: Text(
              equation,
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: AppTheme.accentGold,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 40),

          // Answer Options
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 2,
            children:
                answerOptions
                    .map((answer) => _buildAnswerOption(answer))
                    .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOption(int answer) {
    return GestureDetector(
      onTap: () => _selectAnswer(answer),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.cardDark,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.primaryRed.withOpacity(0.5)),
        ),
        child: Center(
          child: Text(
            answer.toString(),
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }
}
