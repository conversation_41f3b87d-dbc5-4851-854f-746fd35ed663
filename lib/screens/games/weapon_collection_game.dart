import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../models/reward_model.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class WeaponCollectionGame extends StatefulWidget {
  const WeaponCollectionGame({super.key});

  @override
  State<WeaponCollectionGame> createState() => _WeaponCollectionGameState();
}

class _WeaponCollectionGameState extends State<WeaponCollectionGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _weaponTimer;

  int _score = 0;
  int _timeLeft = 45;
  int _weaponsCollected = 0;
  int _totalWeapons = 0;
  bool _gameStarted = false;
  bool _gameEnded = false;

  List<Weapon> _weapons = [];
  final Random _random = Random();
  BannerAd? _bannerAd;

  // Free Fire weapons with their rarity and points
  final List<WeaponData> _weaponTypes = [
    WeaponData('🔫', 'M4A1', 10, Colors.grey, 'Common'),
    WeaponData('🏹', 'Crossbow', 15, Colors.green, 'Uncommon'),
    WeaponData('💥', 'AK47', 20, Colors.blue, 'Rare'),
    WeaponData('⚡', 'AWM', 25, Colors.purple, 'Epic'),
    WeaponData('🔥', 'Groza', 30, Colors.orange, 'Legendary'),
    WeaponData('💎', 'Dragon AK', 50, Colors.red, 'Mythic'),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAds();
  }

  void _initializeAds() {
    _bannerAd = AdMobService.instance.createBannerAd();
    _bannerAd?.load();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _weaponTimer.cancel();
    _bannerAd?.dispose();
    for (var weapon in _weapons) {
      weapon.controller.dispose();
    }
    super.dispose();
  }

  void _startGame() {
    setState(() {
      _gameStarted = true;
      _gameEnded = false;
      _score = 0;
      _timeLeft = 45;
      _weaponsCollected = 0;
      _totalWeapons = 0;
      _weapons.clear();
    });

    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeLeft--;
      });
      if (_timeLeft <= 0) {
        _endGame();
      }
    });

    _weaponTimer = Timer.periodic(const Duration(milliseconds: 800), (timer) {
      _spawnWeapon();
    });
  }

  void _spawnWeapon() {
    if (_gameEnded) return;

    final weaponData = _getRandomWeapon();
    final weapon = Weapon(
      data: weaponData,
      position: Offset(
        _random.nextDouble() * (MediaQuery.of(context).size.width - 80),
        _random.nextDouble() * (MediaQuery.of(context).size.height - 200) + 100,
      ),
      controller: AnimationController(
        duration: const Duration(milliseconds: 2500),
        vsync: this,
      ),
    );

    weapon.controller.forward();
    weapon.controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _weapons.remove(weapon);
        });
        weapon.controller.dispose();
      }
    });

    setState(() {
      _weapons.add(weapon);
      _totalWeapons++;
    });
  }

  WeaponData _getRandomWeapon() {
    // Weighted random selection for rarity
    final rand = _random.nextDouble();
    if (rand < 0.4) return _weaponTypes[0]; // Common 40%
    if (rand < 0.65) return _weaponTypes[1]; // Uncommon 25%
    if (rand < 0.8) return _weaponTypes[2]; // Rare 15%
    if (rand < 0.9) return _weaponTypes[3]; // Epic 10%
    if (rand < 0.97) return _weaponTypes[4]; // Legendary 7%
    return _weaponTypes[5]; // Mythic 3%
  }

  void _collectWeapon(Weapon weapon) {
    setState(() {
      _score += weapon.data.points;
      _weaponsCollected++;
      _weapons.remove(weapon);
    });
    weapon.controller.dispose();
  }

  void _endGame() {
    setState(() {
      _gameEnded = true;
      _gameStarted = false;
    });

    _gameTimer.cancel();
    _weaponTimer.cancel();

    for (var weapon in _weapons) {
      weapon.controller.dispose();
    }
    _weapons.clear();

    _showGameOverDialog();
  }

  void _showGameOverDialog() {
    final accuracy =
        _totalWeapons > 0
            ? (_weaponsCollected / _totalWeapons * 100).round()
            : 0;
    final points = (_score * 0.8).round(); // Convert score to points

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF2A2A2A),
            title: const Text(
              '🏆 Game Over!',
              style: TextStyle(color: AppTheme.accentGold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Weapons Collected: $_weaponsCollected/$_totalWeapons',
                  style: const TextStyle(color: Colors.white),
                ),
                Text(
                  'Accuracy: $accuracy%',
                  style: const TextStyle(color: Colors.white),
                ),
                Text(
                  'Score: $_score',
                  style: const TextStyle(color: Colors.white),
                ),
                Text(
                  'Points Earned: $points',
                  style: const TextStyle(
                    color: AppTheme.accentGold,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAdOption(points);
                },
                child: const Text(
                  'Continue',
                  style: TextStyle(color: AppTheme.accentGold),
                ),
              ),
            ],
          ),
    );
  }

  void _showRewardedAdOption(int points) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF2A2A2A),
            title: const Text(
              '🎁 Bonus Reward!',
              style: TextStyle(color: AppTheme.accentGold),
            ),
            content: Text(
              'Watch an ad to double your points!\n\nCurrent: $points points\nWith ad: ${points * 2} points',
              style: const TextStyle(color: Colors.white),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _awardPoints(points);
                },
                child: const Text('Skip', style: TextStyle(color: Colors.grey)),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAd(points);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentGold,
                ),
                child: const Text(
                  'Watch Ad',
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ],
          ),
    );
  }

  void _showRewardedAd(int basePoints) {
    AdMobService.instance.showRewardedAd(
      onRewarded: (rewardPoints) {
        _awardPoints(basePoints * 2); // Double points for watching ad
      },
      onAdClosed: () {
        // If ad was closed without reward, give base points
        _awardPoints(basePoints);
      },
      onError: (error) {
        _awardPoints(basePoints);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Ad error: $error')));
      },
    );
  }

  void _awardPoints(int points) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.addPoints(
      points,
      RewardType.game,
      metadata: {
        'game': 'weapon_collection',
        'score': _score,
        'weapons_collected': _weaponsCollected,
        'accuracy':
            _totalWeapons > 0
                ? (_weaponsCollected / _totalWeapons * 100).round()
                : 0,
      },
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎉 You earned $points points!'),
        backgroundColor: AppTheme.accentGold,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        title: const Text('🔫 Weapon Collection'),
        backgroundColor: AppTheme.primaryRed,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Banner Ad
          if (_bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: _bannerAd!.size.width.toDouble(),
              height: _bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),

          // Game Info
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildInfoCard('⏱️', 'Time', '$_timeLeft'),
                _buildInfoCard('🎯', 'Score', '$_score'),
                _buildInfoCard('🔫', 'Collected', '$_weaponsCollected'),
              ],
            ),
          ),

          // Game Area
          Expanded(
            child: _gameStarted ? _buildGameArea() : _buildStartScreen(),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String icon, String label, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: AppTheme.cardDecoration,
      child: Column(
        children: [
          Text(icon, style: const TextStyle(fontSize: 20)),
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStartScreen() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.sports_esports,
            size: 80,
            color: AppTheme.accentGold,
          ),
          const SizedBox(height: 20),
          const Text(
            '🔫 Free Fire Weapon Collection',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: AppTheme.cardDecoration,
            child: const Column(
              children: [
                Text(
                  'How to Play:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.accentGold,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  '• Tap weapons as they appear on screen\n'
                  '• Collect rare weapons for more points\n'
                  '• You have 45 seconds to collect as many as possible\n'
                  '• Rarer weapons = More points!\n\n'
                  'Weapon Rarities:\n'
                  '🔫 Common (10 pts) • 🏹 Uncommon (15 pts)\n'
                  '💥 Rare (20 pts) • ⚡ Epic (25 pts)\n'
                  '🔥 Legendary (30 pts) • 💎 Mythic (50 pts)',
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),
          CustomButton(text: 'Start Game', onPressed: _startGame),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1A1A1A),
            const Color(0xFF1A1A1A).withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background pattern
          ...List.generate(20, (index) {
            return Positioned(
              left: (index % 5) * 80.0,
              top: (index ~/ 5) * 120.0,
              child: Icon(
                Icons.blur_circular,
                color: Colors.white.withValues(alpha: 0.05),
                size: 40,
              ),
            );
          }),

          // Weapons
          ..._weapons.map((weapon) => _buildWeapon(weapon)),

          // Game over overlay
          if (_gameEnded)
            Container(
              color: Colors.black.withValues(alpha: 0.7),
              child: const Center(
                child: Text(
                  'Game Over!',
                  style: TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.accentGold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildWeapon(Weapon weapon) {
    return AnimatedBuilder(
      animation: weapon.controller,
      builder: (context, child) {
        final scale = 1.0 - (weapon.controller.value * 0.3);
        final opacity = 1.0 - weapon.controller.value;

        return Positioned(
          left: weapon.position.dx,
          top: weapon.position.dy,
          child: GestureDetector(
            onTap: () => _collectWeapon(weapon),
            child: Transform.scale(
              scale: scale,
              child: Opacity(
                opacity: opacity,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: weapon.data.color.withValues(alpha: 0.2),
                    border: Border.all(color: weapon.data.color, width: 2),
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: weapon.data.color.withValues(alpha: 0.5),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        weapon.data.icon,
                        style: const TextStyle(fontSize: 20),
                      ),
                      Text(
                        '${weapon.data.points}',
                        style: TextStyle(
                          color: weapon.data.color,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Data classes
class WeaponData {
  final String icon;
  final String name;
  final int points;
  final Color color;
  final String rarity;

  WeaponData(this.icon, this.name, this.points, this.color, this.rarity);
}

class Weapon {
  final WeaponData data;
  final Offset position;
  final AnimationController controller;

  Weapon({
    required this.data,
    required this.position,
    required this.controller,
  });
}
