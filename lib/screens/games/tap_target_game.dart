import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/reward_model.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class TapTargetGame extends StatefulWidget {
  const TapTargetGame({super.key});

  @override
  State<TapTargetGame> createState() => _TapTargetGameState();
}

class _TapTargetGameState extends State<TapTargetGame>
    with TickerProviderStateMixin {
  Timer? gameTimer;
  Timer? targetTimer;
  int timeLeft = 30;
  int score = 0;
  int targetsHit = 0;
  int targetsMissed = 0;
  bool gameStarted = false;
  bool gameEnded = false;

  List<Target> targets = [];
  Random random = Random();
  BannerAd? bannerAd;
  bool isBannerAdReady = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    targetTimer?.cancel();
    bannerAd?.dispose();
    for (var target in targets) {
      target.controller.dispose();
    }
    super.dispose();
  }

  void _loadBannerAd() {
    bannerAd = AdMobService.instance.createBannerAd();
    bannerAd!.load().then((_) {
      setState(() {
        isBannerAdReady = true;
      });
    });
  }

  void _startGame() {
    setState(() {
      gameStarted = true;
      timeLeft = 30;
      score = 0;
      targetsHit = 0;
      targetsMissed = 0;
      targets.clear();
    });

    // Start game timer
    gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        timeLeft--;
      });

      if (timeLeft <= 0) {
        _endGame();
      }
    });

    // Start spawning targets
    _spawnTargets();
  }

  void _spawnTargets() {
    targetTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (!gameStarted || gameEnded) {
        timer.cancel();
        return;
      }

      _addTarget();
    });
  }

  void _addTarget() {
    if (targets.length >= 5) return; // Max 5 targets on screen

    final controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    final target = Target(
      id: DateTime.now().millisecondsSinceEpoch,
      x: random.nextDouble() * 0.8 + 0.1, // 10% margin
      y: random.nextDouble() * 0.6 + 0.2, // 20% margin
      controller: controller,
      size: random.nextDouble() * 30 + 40, // 40-70 size
    );

    setState(() {
      targets.add(target);
    });

    // Auto-remove target after animation
    controller.forward().then((_) {
      if (targets.contains(target)) {
        setState(() {
          targets.remove(target);
          targetsMissed++;
        });
        controller.dispose();
      }
    });
  }

  void _hitTarget(Target target) {
    setState(() {
      targets.remove(target);
      targetsHit++;
      score +=
          (100 - (target.controller.value * 50))
              .round(); // More points for faster hits
    });
    target.controller.dispose();
  }

  void _endGame() {
    gameTimer?.cancel();
    targetTimer?.cancel();
    setState(() {
      gameEnded = true;
    });

    // Clear remaining targets
    for (var target in targets) {
      target.controller.dispose();
    }
    targets.clear();

    // Calculate points earned
    int pointsEarned = _calculatePointsEarned();

    if (pointsEarned > 0) {
      _showGameCompleteDialog(pointsEarned);
    } else {
      _showGameOverDialog();
    }
  }

  int _calculatePointsEarned() {
    if (targetsHit >= 20) {
      return AppConstants.maxGamePoints; // 50 points
    } else if (targetsHit >= 15) {
      return 40;
    } else if (targetsHit >= 10) {
      return 30;
    } else if (targetsHit >= 5) {
      return AppConstants.minGamePoints; // 20 points
    }
    return 0;
  }

  void _showGameCompleteDialog(int pointsEarned) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('🎯 Great Shooting!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Targets Hit: $targetsHit'),
                Text(
                  'Accuracy: ${(targetsHit / (targetsHit + targetsMissed) * 100).toStringAsFixed(1)}%',
                ),
                Text('Points Earned: $pointsEarned'),
                const SizedBox(height: 16),
                const Text('Watch an ad for bonus points?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _awardPoints(pointsEarned);
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Skip'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAd(pointsEarned);
                },
                child: const Text('Watch Ad'),
              ),
            ],
          ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('⏰ Time\'s Up!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Targets Hit: $targetsHit'),
                const Text('Keep practicing to improve!'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _restartGame();
                },
                child: const Text('Play Again'),
              ),
            ],
          ),
    );
  }

  void _showRewardedAd(int basePoints) {
    AdMobService.instance.showRewardedAd(
      onRewarded: (bonusPoints) {
        _awardPoints(basePoints + bonusPoints);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Bonus! +$bonusPoints points from ad!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      },
      onAdClosed: () {
        Navigator.of(context).pop();
      },
      onError: (error) {
        _awardPoints(basePoints);
        Navigator.of(context).pop();
      },
    );
  }

  void _awardPoints(int points) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.addPoints(
      points,
      RewardType.game,
      metadata: {
        'game': 'tap_target',
        'score': score,
        'targets_hit': targetsHit,
        'accuracy': targetsHit / (targetsHit + targetsMissed),
      },
    );
  }

  void _restartGame() {
    setState(() {
      gameStarted = false;
      gameEnded = false;
      targets.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        title: const Text('Tap Target'),
        backgroundColor: AppTheme.backgroundBlack,
        actions: [
          if (gameStarted && !gameEnded)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Text(
                  '⏱️ $timeLeft',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Banner Ad
          if (isBannerAdReady && bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: bannerAd!.size.width.toDouble(),
              height: bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: bannerAd!),
            ),

          // Game Stats
          if (gameStarted)
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    'Score: $score',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Hits: $targetsHit',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Missed: $targetsMissed',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),

          // Game Area
          Expanded(
            child: !gameStarted ? _buildStartScreen() : _buildGameArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.gps_fixed, size: 80, color: AppTheme.accentGold),
          const SizedBox(height: 20),
          Text('Tap Target', style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 10),
          const Text(
            'Tap the targets as fast as you can!\n'
            'Hit 20+ targets to earn maximum points.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          CustomButton(
            text: 'Start Game',
            onPressed: _startGame,
            icon: Icons.play_arrow,
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.backgroundBlack,
            AppTheme.backgroundBlack.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: targets.map((target) => _buildTarget(target)).toList(),
      ),
    );
  }

  Widget _buildTarget(Target target) {
    return Positioned(
      left: target.x * MediaQuery.of(context).size.width - target.size / 2,
      top:
          target.y * (MediaQuery.of(context).size.height - 200) -
          target.size / 2,
      child: GestureDetector(
        onTap: () => _hitTarget(target),
        child: AnimatedBuilder(
          animation: target.controller,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 - (target.controller.value * 0.3),
              child: Container(
                width: target.size,
                height: target.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.primaryRed,
                      AppTheme.primaryRed.withOpacity(0.7),
                    ],
                  ),
                  border: Border.all(color: AppTheme.accentGold, width: 3),
                ),
                child: const Center(
                  child: Icon(Icons.gps_fixed, color: Colors.white, size: 20),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class Target {
  final int id;
  final double x;
  final double y;
  final AnimationController controller;
  final double size;

  Target({
    required this.id,
    required this.x,
    required this.y,
    required this.controller,
    required this.size,
  });
}
