import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/quiz_model.dart';
import '../../models/reward_model.dart';
import '../../services/daily_quiz_service.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class QuizQuestionScreen extends StatefulWidget {
  final DailyQuiz quiz;

  const QuizQuestionScreen({super.key, required this.quiz});

  @override
  State<QuizQuestionScreen> createState() => _QuizQuestionScreenState();
}

class _QuizQuestionScreenState extends State<QuizQuestionScreen>
    with TickerProviderStateMixin {
  int _currentQuestionIndex = 0;
  List<DailyQuizAnswer> _answers = [];
  int? _selectedAnswer;
  bool _showExplanation = false;
  bool _isSubmitting = false;
  DateTime? _startTime;
  late AnimationController _progressController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _startTime = DateTime.now();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  QuizQuestion get _currentQuestion =>
      widget.quiz.questions[_currentQuestionIndex];
  bool get _isLastQuestion =>
      _currentQuestionIndex == widget.quiz.questions.length - 1;
  double get _progress =>
      (_currentQuestionIndex + 1) / widget.quiz.questions.length;

  void _selectAnswer(int index) {
    if (_showExplanation) return;

    setState(() {
      _selectedAnswer = index;
    });
  }

  Future<void> _submitAnswer() async {
    if (_selectedAnswer == null) return;

    final question = _currentQuestion;
    final isCorrect = _selectedAnswer == question.correctAnswerIndex;

    final answer = DailyQuizAnswer(
      questionId: question.id,
      selectedAnswer: _selectedAnswer!,
      correctAnswer: question.correctAnswerIndex ?? 0,
      isCorrect: isCorrect,
      pointsEarned: isCorrect ? question.points : 0,
    );

    _answers.add(answer);

    setState(() {
      _showExplanation = true;
    });

    // Update progress
    _progressController.animateTo(_progress);

    // Wait for user to read explanation
    await Future.delayed(const Duration(seconds: 2));

    if (_isLastQuestion) {
      _submitQuiz();
    } else {
      _nextQuestion();
    }
  }

  void _nextQuestion() {
    _fadeController.reset();
    setState(() {
      _currentQuestionIndex++;
      _selectedAnswer = null;
      _showExplanation = false;
    });
    _fadeController.forward();
  }

  Future<void> _submitQuiz() async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final timeTaken = DateTime.now().difference(_startTime!);

      final attempt = await DailyQuizService.submitQuizAttempt(
        userId: userProvider.user!.uid,
        quizDate: widget.quiz.date,
        answers: _answers,
        timeTaken: timeTaken,
      );

      // Award points to user
      int totalPoints = 0;
      for (final answer in _answers) {
        if (answer.isCorrect) {
          totalPoints += answer.pointsEarned;
        }
      }

      if (totalPoints > 0) {
        await userProvider.addPoints(
          totalPoints,
          RewardType.quiz,
          metadata: {
            'quizDate': widget.quiz.date,
            'score': attempt.score,
            'totalQuestions': widget.quiz.questions.length,
          },
        );
      }

      if (mounted) {
        Navigator.pop(context, attempt);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting quiz: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Question ${_currentQuestionIndex + 1} of ${widget.quiz.questions.length}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => _showExitDialog(),
        ),
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: Column(
          children: [
            // Progress Bar
            Container(
              height: 8,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(4),
              ),
              child: AnimatedBuilder(
                animation: _progressController,
                builder: (context, child) {
                  return FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _progressController.value,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppTheme.accentGold,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  );
                },
              ),
            ),

            // Question Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeController,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Question
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(24),
                        decoration: AppTheme.cardDecoration,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.accentGold,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${_currentQuestion.points} pts',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _currentQuestion.question,
                              style: Theme.of(
                                context,
                              ).textTheme.titleLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                height: 1.4,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Options
                      ...List.generate(_currentQuestion.options.length, (
                        index,
                      ) {
                        return _buildOptionCard(index);
                      }),

                      const SizedBox(height: 20),

                      // Explanation (if shown)
                      if (_showExplanation) _buildExplanation(),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),

            // Submit Button
            if (!_showExplanation)
              Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: double.infinity,
                  child: CustomButton(
                    text: _isLastQuestion ? 'FINISH QUIZ' : 'SUBMIT ANSWER',
                    onPressed: _selectedAnswer != null ? _submitAnswer : null,
                    backgroundColor: AppTheme.accentGold,
                    textColor: Colors.black,
                    height: 60,
                  ),
                ),
              ),

            // Loading indicator when submitting
            if (_isSubmitting)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard(int index) {
    final option = _currentQuestion.options[index];
    final isSelected = _selectedAnswer == index;
    final isCorrect = index == _currentQuestion.correctAnswerIndex;

    Color cardColor = AppTheme.cardDark;
    Color borderColor = Colors.transparent;
    Color textColor = Colors.white;
    IconData? icon;

    if (_showExplanation) {
      if (isCorrect) {
        cardColor = Colors.green.withValues(alpha: 0.2);
        borderColor = Colors.green;
        icon = Icons.check_circle;
      } else if (isSelected && !isCorrect) {
        cardColor = Colors.red.withValues(alpha: 0.2);
        borderColor = Colors.red;
        icon = Icons.cancel;
      }
    } else if (isSelected) {
      cardColor = AppTheme.accentGold.withValues(alpha: 0.2);
      borderColor = AppTheme.accentGold;
      textColor = AppTheme.accentGold;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _selectAnswer(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: borderColor, width: 2),
            ),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color:
                        isSelected || _showExplanation
                            ? (isCorrect
                                ? Colors.green
                                : (isSelected ? Colors.red : Colors.grey))
                            : Colors.grey[700],
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child:
                        icon != null
                            ? Icon(icon, color: Colors.white, size: 20)
                            : Text(
                              String.fromCharCode(65 + index), // A, B, C, D
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    option,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: textColor,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExplanation() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.accentGold.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.accentGold.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: AppTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Explanation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.accentGold,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _currentQuestion.explanation,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.white, height: 1.4),
          ),
        ],
      ),
    );
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('Exit Quiz?'),
            content: const Text(
              'Are you sure you want to exit? Your progress will be lost and you won\'t be able to retake this quiz today.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  Navigator.pop(context); // Close quiz
                },
                child: const Text('Exit', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }
}
