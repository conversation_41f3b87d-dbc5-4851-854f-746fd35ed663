import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/reward_model.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class ColorMatchGame extends StatefulWidget {
  const ColorMatchGame({super.key});

  @override
  State<ColorMatchGame> createState() => _ColorMatchGameState();
}

class _ColorMatchGameState extends State<ColorMatchGame> {
  Timer? gameTimer;
  int timeLeft = 45;
  int score = 0;
  int level = 1;
  int correctAnswers = 0;
  int wrongAnswers = 0;
  bool gameStarted = false;
  bool gameEnded = false;

  Color targetColor = Colors.red;
  String targetColorName = '';
  List<ColorOption> colorOptions = [];
  Random random = Random();
  BannerAd? bannerAd;
  bool isBannerAdReady = false;

  final List<ColorData> gameColors = [
    ColorData('Red', Colors.red),
    ColorData('Blue', Colors.blue),
    ColorData('Green', Colors.green),
    ColorData('Yellow', Colors.yellow),
    ColorData('Purple', Colors.purple),
    ColorData('Orange', Colors.orange),
    ColorData('Pink', Colors.pink),
    ColorData('Cyan', Colors.cyan),
    ColorData('Lime', Colors.lime),
    ColorData('Indigo', Colors.indigo),
  ];

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    bannerAd?.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    bannerAd = AdMobService.instance.createBannerAd();
    bannerAd!.load().then((_) {
      setState(() {
        isBannerAdReady = true;
      });
    });
  }

  void _startGame() {
    setState(() {
      gameStarted = true;
      timeLeft = 45;
      score = 0;
      level = 1;
      correctAnswers = 0;
      wrongAnswers = 0;
    });

    _generateRound();

    gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        timeLeft--;
      });

      if (timeLeft <= 0) {
        _endGame();
      }
    });
  }

  void _generateRound() {
    // Select target color
    final targetColorData = gameColors[random.nextInt(gameColors.length)];

    // Create options (3 wrong + 1 correct)
    List<ColorData> options = [targetColorData];

    while (options.length < 4) {
      final option = gameColors[random.nextInt(gameColors.length)];
      if (!options.contains(option)) {
        options.add(option);
      }
    }

    options.shuffle();

    setState(() {
      targetColor = targetColorData.color;
      targetColorName = targetColorData.name;
      colorOptions =
          options
              .map(
                (colorData) => ColorOption(
                  color: colorData.color,
                  name: colorData.name,
                  isCorrect: colorData == targetColorData,
                ),
              )
              .toList();
    });
  }

  void _selectColor(ColorOption option) {
    if (option.isCorrect) {
      setState(() {
        correctAnswers++;
        score += (10 + level * 5); // More points for higher levels

        // Level up every 5 correct answers
        if (correctAnswers % 5 == 0) {
          level++;
        }
      });

      // Show success feedback
      _showFeedback(true);
    } else {
      setState(() {
        wrongAnswers++;
        score =
            (score - 5)
                .clamp(0, double.infinity)
                .toInt(); // Lose points for wrong answers
      });

      // Show error feedback
      _showFeedback(false);
    }

    // Generate next round after delay
    Timer(const Duration(milliseconds: 800), () {
      if (gameStarted && !gameEnded) {
        _generateRound();
      }
    });
  }

  void _showFeedback(bool correct) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder:
          (context) => Center(
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: correct ? AppTheme.successGreen : Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  correct ? '✓ Correct!' : '✗ Wrong!',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
    );

    overlay.insert(overlayEntry);
    Timer(const Duration(milliseconds: 600), () {
      overlayEntry.remove();
    });
  }

  void _endGame() {
    gameTimer?.cancel();
    setState(() {
      gameEnded = true;
    });

    int pointsEarned = _calculatePointsEarned();

    if (pointsEarned > 0) {
      _showGameCompleteDialog(pointsEarned);
    } else {
      _showGameOverDialog();
    }
  }

  int _calculatePointsEarned() {
    if (correctAnswers >= 25) {
      return AppConstants.maxGamePoints; // 50 points
    } else if (correctAnswers >= 20) {
      return 40;
    } else if (correctAnswers >= 15) {
      return 30;
    } else if (correctAnswers >= 10) {
      return AppConstants.minGamePoints; // 20 points
    }
    return 0;
  }

  void _showGameCompleteDialog(int pointsEarned) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('🎨 Color Master!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Level Reached: $level'),
                Text('Correct: $correctAnswers'),
                Text('Wrong: $wrongAnswers'),
                Text(
                  'Accuracy: ${(correctAnswers / (correctAnswers + wrongAnswers) * 100).toStringAsFixed(1)}%',
                ),
                Text('Points Earned: $pointsEarned'),
                const SizedBox(height: 16),
                const Text('Watch an ad for bonus points?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _awardPoints(pointsEarned);
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Skip'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAd(pointsEarned);
                },
                child: const Text('Watch Ad'),
              ),
            ],
          ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('⏰ Time\'s Up!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Level: $level'),
                Text('Correct: $correctAnswers'),
                const Text('Practice makes perfect!'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _restartGame();
                },
                child: const Text('Play Again'),
              ),
            ],
          ),
    );
  }

  void _showRewardedAd(int basePoints) {
    AdMobService.instance.showRewardedAd(
      onRewarded: (bonusPoints) {
        _awardPoints(basePoints + bonusPoints);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Bonus! +$bonusPoints points from ad!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      },
      onAdClosed: () {
        Navigator.of(context).pop();
      },
      onError: (error) {
        _awardPoints(basePoints);
        Navigator.of(context).pop();
      },
    );
  }

  void _awardPoints(int points) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.addPoints(
      points,
      RewardType.game,
      metadata: {
        'game': 'color_match',
        'score': score,
        'level': level,
        'correct_answers': correctAnswers,
        'accuracy': correctAnswers / (correctAnswers + wrongAnswers),
      },
    );
  }

  void _restartGame() {
    setState(() {
      gameStarted = false;
      gameEnded = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        title: const Text('Color Match'),
        backgroundColor: AppTheme.backgroundBlack,
        actions: [
          if (gameStarted && !gameEnded)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Text(
                  '⏱️ $timeLeft',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Banner Ad
          if (isBannerAdReady && bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: bannerAd!.size.width.toDouble(),
              height: bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: bannerAd!),
            ),

          // Game Stats
          if (gameStarted)
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    'Score: $score',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Level: $level',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Correct: $correctAnswers',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),

          Expanded(
            child: !gameStarted ? _buildStartScreen() : _buildGameArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.palette, size: 80, color: AppTheme.accentGold),
          const SizedBox(height: 20),
          Text(
            'Color Match',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 10),
          const Text(
            'Match the color name with the correct color!\n'
            'Get 25+ correct to earn maximum points.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          CustomButton(
            text: 'Start Game',
            onPressed: _startGame,
            icon: Icons.play_arrow,
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Target Color Display
          Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              color: targetColor,
              borderRadius: BorderRadius.circular(75),
              border: Border.all(color: Colors.white, width: 4),
            ),
          ),

          const SizedBox(height: 30),

          Text(
            'What color is this?',
            style: Theme.of(context).textTheme.headlineSmall,
          ),

          const SizedBox(height: 30),

          // Color Options
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 2.5,
            children:
                colorOptions
                    .map((option) => _buildColorOption(option))
                    .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildColorOption(ColorOption option) {
    return GestureDetector(
      onTap: () => _selectColor(option),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.cardDark,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.primaryRed.withOpacity(0.3)),
        ),
        child: Center(
          child: Text(
            option.name,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }
}

class ColorData {
  final String name;
  final Color color;

  ColorData(this.name, this.color);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ColorData &&
          runtimeType == other.runtimeType &&
          name == other.name;

  @override
  int get hashCode => name.hashCode;
}

class ColorOption {
  final Color color;
  final String name;
  final bool isCorrect;

  ColorOption({
    required this.color,
    required this.name,
    required this.isCorrect,
  });
}
