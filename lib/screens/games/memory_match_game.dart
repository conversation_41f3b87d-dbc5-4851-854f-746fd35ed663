import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';
import '../../providers/user_provider.dart';
import '../../services/admob_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/reward_model.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class MemoryMatchGame extends StatefulWidget {
  const MemoryMatchGame({super.key});

  @override
  State<MemoryMatchGame> createState() => _MemoryMatchGameState();
}

class _MemoryMatchGameState extends State<MemoryMatchGame> {
  List<int> cards = [];
  List<bool> flipped = [];
  List<bool> matched = [];
  int? firstCard;
  int? secondCard;
  bool canFlip = true;
  int moves = 0;
  int score = 0;
  Timer? gameTimer;
  int timeLeft = 60;
  bool gameStarted = false;
  bool gameEnded = false;
  BannerAd? bannerAd;
  bool isBannerAdReady = false;

  @override
  void initState() {
    super.initState();
    _initializeGame();
    _loadBannerAd();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    bannerAd?.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    bannerAd = AdMobService.instance.createBannerAd();
    bannerAd!.load().then((_) {
      setState(() {
        isBannerAdReady = true;
      });
    });
  }

  void _initializeGame() {
    // Create pairs of cards (8 pairs = 16 cards)
    cards = [];
    for (int i = 0; i < 8; i++) {
      cards.add(i);
      cards.add(i);
    }
    cards.shuffle();

    flipped = List.filled(16, false);
    matched = List.filled(16, false);
    firstCard = null;
    secondCard = null;
    canFlip = true;
    moves = 0;
    score = 0;
    timeLeft = 60;
    gameStarted = false;
    gameEnded = false;
  }

  void _startGame() {
    setState(() {
      gameStarted = true;
    });

    gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        timeLeft--;
      });

      if (timeLeft <= 0) {
        _endGame();
      }
    });
  }

  void _endGame() {
    gameTimer?.cancel();
    setState(() {
      gameEnded = true;
    });

    // Calculate final score
    int matchedPairs = matched.where((m) => m).length ~/ 2;
    score = (matchedPairs * 100) + (timeLeft * 5) - (moves * 2);
    score = score.clamp(0, 1000);

    // Award points based on performance
    int pointsEarned = _calculatePointsEarned();

    if (pointsEarned > 0) {
      _showGameCompleteDialog(pointsEarned);
    } else {
      _showGameOverDialog();
    }
  }

  int _calculatePointsEarned() {
    int matchedPairs = matched.where((m) => m).length ~/ 2;

    if (matchedPairs >= 6) {
      return AppConstants.maxGamePoints; // 50 points
    } else if (matchedPairs >= 4) {
      return 35;
    } else if (matchedPairs >= 2) {
      return AppConstants.minGamePoints; // 20 points
    }
    return 0;
  }

  void _flipCard(int index) {
    if (!canFlip || flipped[index] || matched[index] || !gameStarted) return;

    setState(() {
      flipped[index] = true;
      moves++;
    });

    if (firstCard == null) {
      firstCard = index;
    } else if (secondCard == null) {
      secondCard = index;
      canFlip = false;

      // Check for match
      Timer(const Duration(milliseconds: 1000), () {
        _checkMatch();
      });
    }
  }

  void _checkMatch() {
    if (firstCard != null && secondCard != null) {
      if (cards[firstCard!] == cards[secondCard!]) {
        // Match found
        setState(() {
          matched[firstCard!] = true;
          matched[secondCard!] = true;
        });

        // Check if game is complete
        if (matched.every((m) => m)) {
          _endGame();
        }
      } else {
        // No match, flip back
        setState(() {
          flipped[firstCard!] = false;
          flipped[secondCard!] = false;
        });
      }

      setState(() {
        firstCard = null;
        secondCard = null;
        canFlip = true;
      });
    }
  }

  void _showGameCompleteDialog(int pointsEarned) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('🎉 Game Complete!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Moves: $moves'),
                Text('Points Earned: $pointsEarned'),
                const SizedBox(height: 16),
                const Text('Watch an ad for bonus points?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _awardPoints(pointsEarned);
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Skip'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showRewardedAd(pointsEarned);
                },
                child: const Text('Watch Ad'),
              ),
            ],
          ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('⏰ Time\'s Up!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $score'),
                Text('Moves: $moves'),
                const Text('Better luck next time!'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _restartGame();
                },
                child: const Text('Play Again'),
              ),
            ],
          ),
    );
  }

  void _showRewardedAd(int basePoints) {
    AdMobService.instance.showRewardedAd(
      onRewarded: (bonusPoints) {
        _awardPoints(basePoints + bonusPoints);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Bonus! +$bonusPoints points from ad!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      },
      onAdClosed: () {
        Navigator.of(context).pop();
      },
      onError: (error) {
        _awardPoints(basePoints);
        Navigator.of(context).pop();
      },
    );
  }

  void _awardPoints(int points) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.addPoints(
      points,
      RewardType.game,
      metadata: {'game': 'memory_match', 'score': score, 'moves': moves},
    );
  }

  void _restartGame() {
    setState(() {
      _initializeGame();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        title: const Text('Memory Match'),
        backgroundColor: AppTheme.backgroundBlack,
        actions: [
          if (gameStarted && !gameEnded)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Text(
                  '⏱️ $timeLeft',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Banner Ad
          if (isBannerAdReady && bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: bannerAd!.size.width.toDouble(),
              height: bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: bannerAd!),
            ),

          // Game Stats
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  'Moves: $moves',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  'Score: $score',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),

          // Game Board
          Expanded(
            child: !gameStarted ? _buildStartScreen() : _buildGameBoard(),
          ),
        ],
      ),
    );
  }

  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.memory, size: 80, color: AppTheme.accentGold),
          const SizedBox(height: 20),
          Text(
            'Memory Match',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 10),
          const Text(
            'Match all pairs before time runs out!\n'
            'Earn 20-50 points based on performance.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          CustomButton(
            text: 'Start Game',
            onPressed: _startGame,
            icon: Icons.play_arrow,
          ),
        ],
      ),
    );
  }

  Widget _buildGameBoard() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: 16,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () => _flipCard(index),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              decoration: BoxDecoration(
                color:
                    flipped[index] || matched[index]
                        ? AppTheme.accentGold
                        : AppTheme.cardDark,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color:
                      matched[index]
                          ? AppTheme.successGreen
                          : AppTheme.primaryRed.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Center(
                child:
                    flipped[index] || matched[index]
                        ? Text(
                          _getCardEmoji(cards[index]),
                          style: const TextStyle(fontSize: 24),
                        )
                        : const Icon(
                          Icons.help_outline,
                          color: AppTheme.textGray,
                          size: 30,
                        ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getCardEmoji(int cardValue) {
    const emojis = ['🔥', '💎', '🎯', '⚡', '🏆', '🎮', '🚀', '⭐'];
    return emojis[cardValue % emojis.length];
  }
}
