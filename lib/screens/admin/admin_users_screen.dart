import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/admin_provider.dart';
import '../../models/admin_models.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class AdminUsersScreen extends StatefulWidget {
  const AdminUsersScreen({super.key});

  @override
  State<AdminUsersScreen> createState() => _AdminUsersScreenState();
}

class _AdminUsersScreenState extends State<AdminUsersScreen> {
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadUsers() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final adminProvider = Provider.of<AdminProvider>(context, listen: false);
      adminProvider.loadUsers();
    });
  }

  void _searchUsers() {
    final adminProvider = Provider.of<AdminProvider>(context, listen: false);
    adminProvider.setUserSearchQuery(_searchController.text.trim());
    adminProvider.searchUsers();
  }

  void _showUserDetailsDialog(AdminUserInfo user) {
    showDialog(
      context: context,
      builder: (context) => UserDetailsDialog(user: user),
    );
  }

  void _showBanUserDialog(AdminUserInfo user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardDark,
        title: Text(
          user.isBanned ? 'Unban User' : 'Ban User',
          style: const TextStyle(color: Colors.white),
        ),
        content: Text(
          user.isBanned 
              ? 'Are you sure you want to unban ${user.name}?'
              : 'Are you sure you want to ban ${user.name}? This will prevent them from accessing the app.',
          style: const TextStyle(color: AppTheme.textGray),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final adminProvider = Provider.of<AdminProvider>(context, listen: false);
              adminProvider.banUser(user.uid, !user.isBanned);
            },
            child: Text(
              user.isBanned ? 'Unban' : 'Ban',
              style: TextStyle(color: user.isBanned ? Colors.green : Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.cardDark,
        title: const Text(
          'User Management',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: _loadUsers,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Consumer<AdminProvider>(
        builder: (context, adminProvider, child) {
          return Column(
            children: [
              // Search Bar
              Container(
                padding: const EdgeInsets.all(16),
                color: AppTheme.cardDark,
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          hintText: 'Search by email...',
                          hintStyle: TextStyle(color: AppTheme.textGray),
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.search, color: AppTheme.textGray),
                        ),
                        onSubmitted: (_) => _searchUsers(),
                      ),
                    ),
                    const SizedBox(width: 12),
                    CustomButton(
                      text: 'Search',
                      onPressed: _searchUsers,
                      isLoading: adminProvider.isLoading,
                    ),
                  ],
                ),
              ),

              // Users List
              Expanded(
                child: adminProvider.isLoading
                    ? const Center(
                        child: CircularProgressIndicator(
                          color: AppTheme.primaryRed,
                        ),
                      )
                    : adminProvider.hasError
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error,
                                  size: 64,
                                  color: Colors.red,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Error loading users',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  adminProvider.errorMessage ?? 'Unknown error',
                                  style: const TextStyle(color: AppTheme.textGray),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _loadUsers,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryRed,
                                  ),
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          )
                        : adminProvider.users.isEmpty
                            ? const Center(
                                child: Text(
                                  'No users found',
                                  style: TextStyle(color: AppTheme.textGray),
                                ),
                              )
                            : ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: adminProvider.users.length,
                                itemBuilder: (context, index) {
                                  final user = adminProvider.users[index];
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    child: Card(
                                      color: AppTheme.cardDark,
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                CircleAvatar(
                                                  backgroundColor: AppTheme.primaryRed,
                                                  child: Text(
                                                    user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        user.name,
                                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                          color: Colors.white,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                      Text(
                                                        user.email,
                                                        style: const TextStyle(
                                                          color: AppTheme.textGray,
                                                          fontSize: 12,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                if (user.isBanned)
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: Colors.red.withValues(alpha: 0.2),
                                                      borderRadius: BorderRadius.circular(4),
                                                      border: Border.all(color: Colors.red),
                                                    ),
                                                    child: const Text(
                                                      'BANNED',
                                                      style: TextStyle(
                                                        color: Colors.red,
                                                        fontSize: 10,
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                    ),
                                                  ),
                                              ],
                                            ),
                                            const SizedBox(height: 16),
                                            
                                            // User Stats
                                            Container(
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: AppTheme.backgroundBlack,
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                              child: Column(
                                                children: [
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: _buildStatItem(
                                                          'Points',
                                                          user.points.toString(),
                                                          Icons.stars,
                                                          AppTheme.accentGold,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: _buildStatItem(
                                                          'Streak',
                                                          user.dailyStreak.toString(),
                                                          Icons.local_fire_department,
                                                          Colors.orange,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 8),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: _buildStatItem(
                                                          'Spins',
                                                          user.totalSpins.toString(),
                                                          Icons.casino,
                                                          Colors.purple,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: _buildStatItem(
                                                          'Quizzes',
                                                          user.totalQuizzes.toString(),
                                                          Icons.quiz,
                                                          Colors.blue,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            
                                            const SizedBox(height: 12),
                                            
                                            // Last Login
                                            Row(
                                              children: [
                                                const Icon(
                                                  Icons.access_time,
                                                  color: AppTheme.textGray,
                                                  size: 16,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'Last login: ${_formatDateTime(user.lastLogin)}',
                                                  style: const TextStyle(
                                                    color: AppTheme.textGray,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            
                                            const SizedBox(height: 16),
                                            
                                            // Action Buttons
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: CustomButton(
                                                    text: 'Details',
                                                    onPressed: () => _showUserDetailsDialog(user),
                                                    backgroundColor: Colors.blue,
                                                    icon: Icons.info,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: CustomButton(
                                                    text: user.isBanned ? 'Unban' : 'Ban',
                                                    onPressed: () => _showBanUserDialog(user),
                                                    backgroundColor: user.isBanned ? Colors.green : Colors.red,
                                                    icon: user.isBanned ? Icons.check : Icons.block,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: AppTheme.textGray,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}

class UserDetailsDialog extends StatelessWidget {
  final AdminUserInfo user;

  const UserDetailsDialog({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.cardDark,
      title: Text(
        'User Details',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow('Name', user.name),
            _buildDetailRow('Email', user.email),
            _buildDetailRow('UID', user.uid),
            _buildDetailRow('Points', user.points.toString()),
            _buildDetailRow('Daily Streak', user.dailyStreak.toString()),
            _buildDetailRow('Referral Code', user.referralCode),
            _buildDetailRow('Total Spins', user.totalSpins.toString()),
            _buildDetailRow('Total Quizzes', user.totalQuizzes.toString()),
            _buildDetailRow('Status', user.isBanned ? 'Banned' : 'Active'),
            _buildDetailRow('Created', _formatDateTime(user.createdAt)),
            _buildDetailRow('Last Login', _formatDateTime(user.lastLogin)),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppTheme.textGray,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
