import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/admin_provider.dart';
import '../../models/admin_models.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class AdminRedemptionsScreen extends StatefulWidget {
  const AdminRedemptionsScreen({super.key});

  @override
  State<AdminRedemptionsScreen> createState() => _AdminRedemptionsScreenState();
}

class _AdminRedemptionsScreenState extends State<AdminRedemptionsScreen> {
  final List<String> _statusFilters = [
    'all',
    'pending',
    'approved',
    'rejected',
  ];

  @override
  void initState() {
    super.initState();
    _loadRedemptionRequests();
  }

  void _loadRedemptionRequests() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final adminProvider = Provider.of<AdminProvider>(context, listen: false);
      adminProvider.loadRedemptionRequests();
    });
  }

  void _showProcessDialog(AdminRedemptionRequest request) {
    showDialog(
      context: context,
      builder: (context) => ProcessRedemptionDialog(request: request),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.cardDark,
        title: const Text(
          'Redemption Requests',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: _loadRedemptionRequests,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Consumer<AdminProvider>(
        builder: (context, adminProvider, child) {
          return Column(
            children: [
              // Status Filter
              Container(
                padding: const EdgeInsets.all(16),
                color: AppTheme.cardDark,
                child: DropdownButtonFormField<String>(
                  value: adminProvider.selectedRedemptionStatus,
                  decoration: const InputDecoration(
                    labelText: 'Filter by Status',
                    labelStyle: TextStyle(color: AppTheme.textGray),
                    border: OutlineInputBorder(),
                  ),
                  dropdownColor: AppTheme.cardDark,
                  style: const TextStyle(color: Colors.white),
                  items:
                      _statusFilters.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(status.toUpperCase()),
                        );
                      }).toList(),
                  onChanged: (value) {
                    adminProvider.setRedemptionStatusFilter(value!);
                  },
                ),
              ),

              // Requests List
              Expanded(
                child:
                    adminProvider.isLoading
                        ? const Center(
                          child: CircularProgressIndicator(
                            color: AppTheme.primaryRed,
                          ),
                        )
                        : adminProvider.hasError
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error, size: 64, color: Colors.red),
                              const SizedBox(height: 16),
                              Text(
                                'Error loading requests',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(color: Colors.white),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                adminProvider.errorMessage ?? 'Unknown error',
                                style: const TextStyle(
                                  color: AppTheme.textGray,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadRedemptionRequests,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryRed,
                                ),
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                        : adminProvider.redemptionRequests.isEmpty
                        ? const Center(
                          child: Text(
                            'No redemption requests found',
                            style: TextStyle(color: AppTheme.textGray),
                          ),
                        )
                        : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: adminProvider.redemptionRequests.length,
                          itemBuilder: (context, index) {
                            final request =
                                adminProvider.redemptionRequests[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Card(
                                color: AppTheme.cardDark,
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  request.userName,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleMedium
                                                      ?.copyWith(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                ),
                                                Text(
                                                  request.userEmail,
                                                  style: const TextStyle(
                                                    color: AppTheme.textGray,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 6,
                                            ),
                                            decoration: BoxDecoration(
                                              color: _getStatusColor(
                                                request.status,
                                              ).withValues(alpha: 0.2),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              border: Border.all(
                                                color: _getStatusColor(
                                                  request.status,
                                                ),
                                              ),
                                            ),
                                            child: Text(
                                              request.status.toUpperCase(),
                                              style: TextStyle(
                                                color: _getStatusColor(
                                                  request.status,
                                                ),
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),

                                      // Request Details
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: AppTheme.backgroundBlack,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Column(
                                          children: [
                                            Row(
                                              children: [
                                                const Icon(
                                                  Icons.diamond,
                                                  color: AppTheme.accentGold,
                                                  size: 20,
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  '${request.diamondCount} Diamonds',
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              children: [
                                                const Icon(
                                                  Icons.games,
                                                  color: AppTheme.primaryRed,
                                                  size: 20,
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  'Free Fire UID: ${request.freeFireUID}',
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            Row(
                                              children: [
                                                const Icon(
                                                  Icons.access_time,
                                                  color: AppTheme.textGray,
                                                  size: 20,
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  _formatDateTime(
                                                    request.requestDate,
                                                  ),
                                                  style: const TextStyle(
                                                    color: AppTheme.textGray,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),

                                      if (request.adminNotes != null) ...[
                                        const SizedBox(height: 12),
                                        Container(
                                          width: double.infinity,
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withValues(
                                              alpha: 0.1,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            border: Border.all(
                                              color: Colors.blue.withValues(
                                                alpha: 0.3,
                                              ),
                                            ),
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                'Admin Notes:',
                                                style: TextStyle(
                                                  color: Colors.blue,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 12,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                request.adminNotes!,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],

                                      if (request.status == 'pending') ...[
                                        const SizedBox(height: 16),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: CustomButton(
                                                text: 'Reject',
                                                onPressed:
                                                    () => _showProcessDialog(
                                                      request,
                                                    ),
                                                backgroundColor: Colors.red,
                                                icon: Icons.close,
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Expanded(
                                              child: CustomButton(
                                                text: 'Approve',
                                                onPressed:
                                                    () => _showProcessDialog(
                                                      request,
                                                    ),
                                                backgroundColor: Colors.green,
                                                icon: Icons.check,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return AppTheme.textGray;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class ProcessRedemptionDialog extends StatefulWidget {
  final AdminRedemptionRequest request;

  const ProcessRedemptionDialog({super.key, required this.request});

  @override
  State<ProcessRedemptionDialog> createState() =>
      _ProcessRedemptionDialogState();
}

class _ProcessRedemptionDialogState extends State<ProcessRedemptionDialog> {
  final _notesController = TextEditingController();
  String _selectedAction = 'approve';

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _processRequest() async {
    final adminProvider = Provider.of<AdminProvider>(context, listen: false);

    await adminProvider.updateRedemptionStatus(
      widget.request.id,
      _selectedAction,
      _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim(),
    );

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.cardDark,
      title: Text(
        'Process Redemption Request',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Request Summary
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.backgroundBlack,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'User: ${widget.request.userName}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Diamonds: ${widget.request.diamondCount}',
                  style: const TextStyle(color: AppTheme.accentGold),
                ),
                Text(
                  'Free Fire UID: ${widget.request.freeFireUID}',
                  style: const TextStyle(color: AppTheme.textGray),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Action Selection
          const Text(
            'Action:',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text(
                    'Approve',
                    style: TextStyle(color: Colors.green),
                  ),
                  value: 'approved',
                  groupValue: _selectedAction,
                  onChanged: (value) {
                    setState(() {
                      _selectedAction = value!;
                    });
                  },
                  activeColor: Colors.green,
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text(
                    'Reject',
                    style: TextStyle(color: Colors.red),
                  ),
                  value: 'rejected',
                  groupValue: _selectedAction,
                  onChanged: (value) {
                    setState(() {
                      _selectedAction = value!;
                    });
                  },
                  activeColor: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Admin Notes
          TextField(
            controller: _notesController,
            style: const TextStyle(color: Colors.white),
            decoration: const InputDecoration(
              labelText: 'Admin Notes (Optional)',
              labelStyle: TextStyle(color: AppTheme.textGray),
              border: OutlineInputBorder(),
              hintText: 'Add any notes about this decision...',
              hintStyle: TextStyle(color: AppTheme.textGray),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        Consumer<AdminProvider>(
          builder: (context, adminProvider, child) {
            return CustomButton(
              text: _selectedAction == 'approved' ? 'Approve' : 'Reject',
              onPressed: adminProvider.isLoading ? null : _processRequest,
              isLoading: adminProvider.isLoading,
              backgroundColor:
                  _selectedAction == 'approved' ? Colors.green : Colors.red,
            );
          },
        ),
      ],
    );
  }
}
