import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

class RedemptionManagementScreen extends StatefulWidget {
  const RedemptionManagementScreen({super.key});

  @override
  State<RedemptionManagementScreen> createState() => _RedemptionManagementScreenState();
}

class _RedemptionManagementScreenState extends State<RedemptionManagementScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Redemption Management',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.redeem,
                size: 80,
                color: AppTheme.accentGold,
              ),
              Sized<PERSON><PERSON>(height: 20),
              Text(
                'Redemption Management',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 10),
              Text(
                'Coming Soon',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
