import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/admin_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/admin_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import 'admin_quiz_screen.dart';
import 'admin_redemptions_screen.dart';
import 'admin_tips_screen.dart';
import 'admin_users_screen.dart';
import 'admin_analytics_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  int _selectedIndex = 0;

  final List<AdminDashboardItem> _dashboardItems = [
    AdminDashboardItem(
      title: 'Analytics',
      subtitle: 'App statistics and insights',
      icon: Icons.analytics,
      color: Colors.blue,
    ),
    AdminDashboardItem(
      title: 'Quiz Management',
      subtitle: 'Manage quiz questions and categories',
      icon: Icons.quiz,
      color: Colors.green,
    ),
    AdminDashboardItem(
      title: 'Redemptions',
      subtitle: 'Approve diamond redemption requests',
      icon: Icons.diamond,
      color: Colors.purple,
    ),
    AdminDashboardItem(
      title: 'Tips & Tricks',
      subtitle: 'Manage Free Fire tips and guides',
      icon: Icons.lightbulb,
      color: Colors.orange,
    ),
    AdminDashboardItem(
      title: 'User Management',
      subtitle: 'View and manage user accounts',
      icon: Icons.people,
      color: Colors.teal,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Check if admin has proper Firebase permissions
      final hasPermissions = await AdminService.hasAdminPermissions();
      if (!hasPermissions && mounted) {
        _showPermissionErrorDialog();
        return;
      }

      if (mounted) {
        final adminProvider = Provider.of<AdminProvider>(
          context,
          listen: false,
        );
        adminProvider.loadAnalytics();
      }
    });
  }

  void _showPermissionErrorDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text(
              'Firebase Rules Not Deployed',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'The Firebase security rules for admin access have not been deployed yet.\n\n'
              'Please deploy the firestore.rules file to enable admin functionality.\n\n'
              'Run: firebase deploy --only firestore:rules',
              style: TextStyle(color: AppTheme.textGray),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop(); // Go back to previous screen
                },
                child: Text('OK', style: TextStyle(color: AppTheme.primaryRed)),
              ),
            ],
          ),
    );
  }

  void _navigateToSection(int index) {
    Widget screen;
    switch (index) {
      case 0:
        screen = const AdminAnalyticsScreen();
        break;
      case 1:
        screen = const AdminQuizScreen();
        break;
      case 2:
        screen = const AdminRedemptionsScreen();
        break;
      case 3:
        screen = const AdminTipsScreen();
        break;
      case 4:
        screen = const AdminUsersScreen();
        break;
      default:
        return;
    }

    Navigator.of(context).push(MaterialPageRoute(builder: (_) => screen));
  }

  Future<void> _signOut() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.signOut();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/admin-login');
    }
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text(
              'Sign Out',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'Are you sure you want to sign out of the admin panel?',
              style: TextStyle(color: AppTheme.textGray),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _signOut();
                },
                child: Text(
                  'Sign Out',
                  style: TextStyle(color: AppTheme.primaryRed),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: AppTheme.primaryGradient,
                      ),
                      child: const Icon(
                        Icons.admin_panel_settings,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppConstants.adminPanelTitle,
                            style: Theme.of(
                              context,
                            ).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Welcome, Admin',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: AppTheme.textGray),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: _showSignOutDialog,
                      icon: const Icon(Icons.logout, color: AppTheme.textGray),
                    ),
                  ],
                ),
              ),

              // Quick Stats
              Consumer<AdminProvider>(
                builder: (context, adminProvider, child) {
                  final analytics = adminProvider.analytics;
                  if (analytics == null) {
                    return const SizedBox.shrink();
                  }

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'Total Users',
                            analytics.totalUsers.toString(),
                            Icons.people,
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            'Daily Active',
                            analytics.dailyActiveUsers.toString(),
                            Icons.trending_up,
                            Colors.green,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            'Pending',
                            analytics.pendingRedemptions.toString(),
                            Icons.pending,
                            Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 24),

              // Dashboard Items
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  itemCount: _dashboardItems.length,
                  itemBuilder: (context, index) {
                    final item = _dashboardItems[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => _navigateToSection(index),
                          borderRadius: BorderRadius.circular(16),
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: AppTheme.cardDark,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: item.color.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: item.color.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    item.icon,
                                    color: item.color,
                                    size: 30,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        item.title,
                                        style: Theme.of(
                                          context,
                                        ).textTheme.titleMedium?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        item.subtitle,
                                        style: Theme.of(
                                          context,
                                        ).textTheme.bodyMedium?.copyWith(
                                          color: AppTheme.textGray,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  color: AppTheme.textGray,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardDark,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textGray),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class AdminDashboardItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  AdminDashboardItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}
