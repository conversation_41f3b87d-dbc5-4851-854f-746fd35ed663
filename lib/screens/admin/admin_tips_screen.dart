import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/admin_provider.dart';
import '../../models/admin_models.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';

class AdminTipsScreen extends StatefulWidget {
  const AdminTipsScreen({super.key});

  @override
  State<AdminTipsScreen> createState() => _AdminTipsScreenState();
}

class _AdminTipsScreenState extends State<AdminTipsScreen> {
  @override
  void initState() {
    super.initState();
    _loadTips();
  }

  void _loadTips() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final adminProvider = Provider.of<AdminProvider>(context, listen: false);
      adminProvider.loadTips();
    });
  }

  void _showAddTipDialog() {
    showDialog(context: context, builder: (context) => const AddTipDialog());
  }

  void _showEditTipDialog(AdminTip tip) {
    showDialog(context: context, builder: (context) => AddTipDialog(tip: tip));
  }

  void _deleteTip(AdminTip tip) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text(
              'Delete Tip',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(
              'Are you sure you want to delete this tip?\n\n"${tip.title}"',
              style: const TextStyle(color: AppTheme.textGray),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  final adminProvider = Provider.of<AdminProvider>(
                    context,
                    listen: false,
                  );
                  adminProvider.deleteTip(tip.id);
                },
                child: Text('Delete', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.cardDark,
        title: const Text(
          'Tips & Tricks Management',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(onPressed: _showAddTipDialog, icon: const Icon(Icons.add)),
        ],
      ),
      body: Consumer<AdminProvider>(
        builder: (context, adminProvider, child) {
          return Column(
            children: [
              // Category Filter
              Container(
                padding: const EdgeInsets.all(16),
                color: AppTheme.cardDark,
                child: DropdownButtonFormField<String>(
                  value:
                      adminProvider.selectedTipCategory.isEmpty
                          ? 'All'
                          : adminProvider.selectedTipCategory,
                  decoration: const InputDecoration(
                    labelText: 'Filter by Category',
                    labelStyle: TextStyle(color: AppTheme.textGray),
                    border: OutlineInputBorder(),
                  ),
                  dropdownColor: AppTheme.cardDark,
                  style: const TextStyle(color: Colors.white),
                  items:
                      ['All', ...AppConstants.tipsCategories].map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                  onChanged: (value) {
                    adminProvider.setTipCategoryFilter(
                      value == 'All' ? '' : value!,
                    );
                  },
                ),
              ),

              // Tips List
              Expanded(
                child:
                    adminProvider.isLoading
                        ? const Center(
                          child: CircularProgressIndicator(
                            color: AppTheme.primaryRed,
                          ),
                        )
                        : adminProvider.hasError
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error, size: 64, color: Colors.red),
                              const SizedBox(height: 16),
                              Text(
                                'Error loading tips',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(color: Colors.white),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                adminProvider.errorMessage ?? 'Unknown error',
                                style: const TextStyle(
                                  color: AppTheme.textGray,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadTips,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryRed,
                                ),
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                        : adminProvider.tips.isEmpty
                        ? const Center(
                          child: Text(
                            'No tips found',
                            style: TextStyle(color: AppTheme.textGray),
                          ),
                        )
                        : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: adminProvider.tips.length,
                          itemBuilder: (context, index) {
                            final tip = adminProvider.tips[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Card(
                                color: AppTheme.cardDark,
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  tip.title,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleMedium
                                                      ?.copyWith(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                ),
                                                const SizedBox(height: 4),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 4,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color: AppTheme.primaryRed
                                                        .withValues(alpha: 0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          4,
                                                        ),
                                                  ),
                                                  child: Text(
                                                    tip.category,
                                                    style: TextStyle(
                                                      color:
                                                          AppTheme.primaryRed,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          PopupMenuButton<String>(
                                            color: AppTheme.cardDark,
                                            onSelected: (value) {
                                              if (value == 'edit') {
                                                _showEditTipDialog(tip);
                                              } else if (value == 'delete') {
                                                _deleteTip(tip);
                                              }
                                            },
                                            itemBuilder:
                                                (context) => [
                                                  const PopupMenuItem(
                                                    value: 'edit',
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons.edit,
                                                          color: Colors.white,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          'Edit',
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const PopupMenuItem(
                                                    value: 'delete',
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons.delete,
                                                          color: Colors.red,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          'Delete',
                                                          style: TextStyle(
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 12),

                                      // Content Preview
                                      Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: AppTheme.backgroundBlack,
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Text(
                                          tip.content.length > 150
                                              ? '${tip.content.substring(0, 150)}...'
                                              : tip.content,
                                          style: const TextStyle(
                                            color: AppTheme.textGray,
                                          ),
                                        ),
                                      ),

                                      const SizedBox(height: 12),

                                      // Metadata
                                      Row(
                                        children: [
                                          Icon(
                                            tip.isActive
                                                ? Icons.visibility
                                                : Icons.visibility_off,
                                            color:
                                                tip.isActive
                                                    ? Colors.green
                                                    : Colors.red,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            tip.isActive
                                                ? 'Active'
                                                : 'Inactive',
                                            style: TextStyle(
                                              color:
                                                  tip.isActive
                                                      ? Colors.green
                                                      : Colors.red,
                                              fontSize: 12,
                                            ),
                                          ),
                                          const Spacer(),
                                          Text(
                                            _formatDateTime(tip.createdAt),
                                            style: const TextStyle(
                                              color: AppTheme.textGray,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          );
        },
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}

class AddTipDialog extends StatefulWidget {
  final AdminTip? tip;

  const AddTipDialog({super.key, this.tip});

  @override
  State<AddTipDialog> createState() => _AddTipDialogState();
}

class _AddTipDialogState extends State<AddTipDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();

  String _selectedCategory = 'Weapons';
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    if (widget.tip != null) {
      _titleController.text = widget.tip!.title;
      _contentController.text = widget.tip!.content;
      _selectedCategory = widget.tip!.category;
      _isActive = widget.tip!.isActive;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _saveTip() async {
    if (!_formKey.currentState!.validate()) return;

    final tip = AdminTip(
      id: widget.tip?.id ?? '',
      title: _titleController.text.trim(),
      content: _contentController.text.trim(),
      category: _selectedCategory,
      createdAt: widget.tip?.createdAt ?? DateTime.now(),
      updatedAt: widget.tip != null ? DateTime.now() : null,
      isActive: _isActive,
    );

    final adminProvider = Provider.of<AdminProvider>(context, listen: false);

    if (widget.tip != null) {
      await adminProvider.updateTip(tip);
    } else {
      await adminProvider.addTip(tip);
    }

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppTheme.cardDark,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.tip != null ? 'Edit Tip' : 'Add New Tip',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Title
                      TextFormField(
                        controller: _titleController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          labelText: 'Title',
                          labelStyle: TextStyle(color: AppTheme.textGray),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a title';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Category
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          labelStyle: TextStyle(color: AppTheme.textGray),
                          border: OutlineInputBorder(),
                        ),
                        dropdownColor: AppTheme.cardDark,
                        style: const TextStyle(color: Colors.white),
                        items:
                            AppConstants.tipsCategories.map((category) {
                              return DropdownMenuItem(
                                value: category,
                                child: Text(category),
                              );
                            }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Content
                      TextFormField(
                        controller: _contentController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          labelText: 'Content',
                          labelStyle: TextStyle(color: AppTheme.textGray),
                          border: OutlineInputBorder(),
                          alignLabelWithHint: true,
                        ),
                        maxLines: 8,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter content';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Active Status
                      Row(
                        children: [
                          Checkbox(
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value!;
                              });
                            },
                            activeColor: AppTheme.primaryRed,
                          ),
                          const Text(
                            'Active (visible to users)',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Consumer<AdminProvider>(
                      builder: (context, adminProvider, child) {
                        return CustomButton(
                          text: widget.tip != null ? 'Update' : 'Add',
                          onPressed: adminProvider.isLoading ? null : _saveTip,
                          isLoading: adminProvider.isLoading,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
