import 'package:flutter/material.dart';
import '../../services/admin_service.dart';
import '../../models/admin_models.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class SettingsManagementScreen extends StatefulWidget {
  const SettingsManagementScreen({super.key});

  @override
  State<SettingsManagementScreen> createState() =>
      _SettingsManagementScreenState();
}

class _SettingsManagementScreenState extends State<SettingsManagementScreen> {
  AppSettings? _settings;
  bool _isLoading = true;
  bool _isSaving = false;

  // Controllers for form fields
  final _pointsToRupeesController = TextEditingController();
  final _pointsToDiamondsController = TextEditingController();
  final _dailyLoginPointsController = TextEditingController();
  final _spinCooldownController = TextEditingController();
  final _maxDailyChestsController = TextEditingController();
  final _maintenanceMessageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _pointsToRupeesController.dispose();
    _pointsToDiamondsController.dispose();
    _dailyLoginPointsController.dispose();
    _spinCooldownController.dispose();
    _maxDailyChestsController.dispose();
    _maintenanceMessageController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      final settings = await AdminService.getAppSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });

      if (settings != null) {
        _populateControllers(settings);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _populateControllers(AppSettings settings) {
    _pointsToRupeesController.text = settings.pointsToRupeesRate.toString();
    _pointsToDiamondsController.text = settings.pointsToDiamondsRate.toString();
    _dailyLoginPointsController.text = settings.dailyLoginBasePoints.toString();
    _spinCooldownController.text = settings.spinCooldownHours.toString();
    _maxDailyChestsController.text = settings.maxDailyChests.toString();
    _maintenanceMessageController.text = settings.maintenanceMessage;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'App Settings',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadSettings,
          ),
        ],
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child:
            _isLoading
                ? const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.accentGold,
                    ),
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Conversion Rates Section
                      _buildSectionCard(
                        'Conversion Rates',
                        Icons.currency_exchange,
                        [
                          _buildNumberField(
                            'Points to Rupees Rate',
                            'How many points = 1 rupee',
                            _pointsToRupeesController,
                            isDecimal: true,
                          ),
                          const SizedBox(height: 16),
                          _buildNumberField(
                            'Points to Diamonds Rate',
                            'How many points = 1 diamond',
                            _pointsToDiamondsController,
                            isDecimal: true,
                          ),
                        ],
                      ),

                      const SizedBox(height: 20),

                      // Game Settings Section
                      _buildSectionCard('Game Settings', Icons.games, [
                        _buildNumberField(
                          'Daily Login Base Points',
                          'Points awarded for daily login',
                          _dailyLoginPointsController,
                        ),
                        const SizedBox(height: 16),
                        _buildNumberField(
                          'Spin Cooldown (Hours)',
                          'Hours between spin wheel uses',
                          _spinCooldownController,
                        ),
                        const SizedBox(height: 16),
                        _buildNumberField(
                          'Max Daily Chests',
                          'Maximum chests per day',
                          _maxDailyChestsController,
                        ),
                      ]),

                      const SizedBox(height: 20),

                      // Maintenance Mode Section
                      _buildSectionCard('Maintenance Mode', Icons.build, [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'Maintenance Mode',
                                style: Theme.of(
                                  context,
                                ).textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Switch(
                              value: _settings?.maintenanceMode ?? false,
                              onChanged: (value) {
                                setState(() {
                                  _settings = _settings?.copyWith(
                                    maintenanceMode: value,
                                  );
                                });
                              },
                              activeColor: AppTheme.accentGold,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: _maintenanceMessageController,
                          style: const TextStyle(color: Colors.white),
                          decoration: InputDecoration(
                            labelText: 'Maintenance Message',
                            labelStyle: const TextStyle(
                              color: AppTheme.accentGold,
                            ),
                            hintText:
                                'Message shown to users during maintenance',
                            hintStyle: const TextStyle(color: Colors.grey),
                            filled: true,
                            fillColor: AppTheme.cardDark.withValues(alpha: 0.5),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(
                                color: AppTheme.accentGold,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(
                                color: AppTheme.accentGold,
                              ),
                            ),
                          ),
                          maxLines: 3,
                        ),
                      ]),

                      const SizedBox(height: 20),

                      // Settings Info
                      if (_settings != null)
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppTheme.cardDark.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.grey.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Settings Information',
                                style: TextStyle(
                                  color: AppTheme.accentGold,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Last Updated: ${_settings!.lastUpdated.toString().substring(0, 19)}',
                                style: const TextStyle(color: Colors.grey),
                              ),
                              Text(
                                'Updated By: ${_settings!.updatedBy}',
                                style: const TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 30),

                      // Save Button
                      SizedBox(
                        width: double.infinity,
                        child: CustomButton(
                          text: _isSaving ? 'Saving...' : 'Save Settings',
                          icon: _isSaving ? null : Icons.save,
                          onPressed: _isSaving ? null : _saveSettings,
                          backgroundColor: AppTheme.accentGold,
                          textColor: Colors.black,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Quick Actions
                      Row(
                        children: [
                          Expanded(
                            child: CustomButton(
                              text: 'Reset to\nDefaults',
                              icon: Icons.restore,
                              onPressed: _resetToDefaults,
                              backgroundColor: Colors.orange,
                              textColor: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: CustomButton(
                              text: 'Export\nSettings',
                              icon: Icons.download,
                              onPressed: _exportSettings,
                              backgroundColor: AppTheme.cardDark,
                              textColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppTheme.accentGold, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    String hint,
    TextEditingController controller, {
    bool isDecimal = false,
  }) {
    return TextField(
      controller: controller,
      keyboardType:
          isDecimal
              ? const TextInputType.numberWithOptions(decimal: true)
              : TextInputType.number,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: AppTheme.accentGold),
        hintText: hint,
        hintStyle: const TextStyle(color: Colors.grey),
        filled: true,
        fillColor: AppTheme.cardDark.withValues(alpha: 0.5),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.accentGold),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.accentGold),
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    if (_settings == null) return;

    // Validate inputs
    final pointsToRupees = double.tryParse(_pointsToRupeesController.text);
    final pointsToDiamonds = double.tryParse(_pointsToDiamondsController.text);
    final dailyLoginPoints = int.tryParse(_dailyLoginPointsController.text);
    final spinCooldown = int.tryParse(_spinCooldownController.text);
    final maxDailyChests = int.tryParse(_maxDailyChestsController.text);

    if (pointsToRupees == null || pointsToRupees <= 0) {
      _showError('Please enter a valid points to rupees rate');
      return;
    }

    if (pointsToDiamonds == null || pointsToDiamonds <= 0) {
      _showError('Please enter a valid points to diamonds rate');
      return;
    }

    if (dailyLoginPoints == null || dailyLoginPoints < 0) {
      _showError('Please enter a valid daily login points value');
      return;
    }

    if (spinCooldown == null || spinCooldown < 0) {
      _showError('Please enter a valid spin cooldown value');
      return;
    }

    if (maxDailyChests == null || maxDailyChests < 0) {
      _showError('Please enter a valid max daily chests value');
      return;
    }

    setState(() => _isSaving = true);

    try {
      final updatedSettings = _settings!.copyWith(
        pointsToRupeesRate: pointsToRupees,
        pointsToDiamondsRate: pointsToDiamonds,
        dailyLoginBasePoints: dailyLoginPoints,
        spinCooldownHours: spinCooldown,
        maxDailyChests: maxDailyChests,
        maintenanceMessage: _maintenanceMessageController.text.trim(),
      );

      await AdminService.updateAppSettings(updatedSettings);

      setState(() {
        _settings = updatedSettings;
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      setState(() => _isSaving = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text(
              'Reset to Defaults',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
              style: TextStyle(color: Colors.grey),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text(
                  'Reset',
                  style: TextStyle(color: Colors.orange),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _pointsToRupeesController.text = '100.0';
        _pointsToDiamondsController.text = '50.0';
        _dailyLoginPointsController.text = '50';
        _spinCooldownController.text = '24';
        _maxDailyChestsController.text = '5';
        _maintenanceMessageController.text = '';
        _settings = _settings?.copyWith(
          maintenanceMode: false,
          maintenanceMessage: '',
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings reset to defaults'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Future<void> _exportSettings() async {
    if (_settings == null) return;

    try {
      final settingsData = _settings!.toFirestore();
      final settingsText = settingsData.entries
          .map((e) => '${e.key}: ${e.value}')
          .join('\n');

      showDialog(
        context: context,
        builder:
            (context) => Dialog(
              backgroundColor: AppTheme.cardDark,
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Current Settings',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.cardDark.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          settingsText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Close'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
