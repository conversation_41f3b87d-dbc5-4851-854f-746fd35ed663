import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/admin_provider.dart';
import '../../models/admin_models.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class AdminQuizScreen extends StatefulWidget {
  const AdminQuizScreen({super.key});

  @override
  State<AdminQuizScreen> createState() => _AdminQuizScreenState();
}

class _AdminQuizScreenState extends State<AdminQuizScreen> {
  final List<String> _categories = [
    'All',
    'Weapons',
    'Maps',
    'Characters',
    'Gameplay',
    'General',
  ];
  final List<String> _difficulties = ['All', 'Easy', 'Medium', 'Hard'];

  @override
  void initState() {
    super.initState();
    _loadQuizQuestions();
  }

  void _loadQuizQuestions() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final adminProvider = Provider.of<AdminProvider>(context, listen: false);
      adminProvider.loadQuizQuestions();
    });
  }

  void _showAddQuestionDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddQuestionDialog(),
    );
  }

  void _showEditQuestionDialog(AdminQuizQuestion question) {
    showDialog(
      context: context,
      builder: (context) => AddQuestionDialog(question: question),
    );
  }

  void _deleteQuestion(AdminQuizQuestion question) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text(
              'Delete Question',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(
              'Are you sure you want to delete this question?\n\n"${question.question}"',
              style: const TextStyle(color: AppTheme.textGray),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  final adminProvider = Provider.of<AdminProvider>(
                    context,
                    listen: false,
                  );
                  adminProvider.deleteQuizQuestion(question.id);
                },
                child: Text('Delete', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.cardDark,
        title: const Text(
          'Quiz Management',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: _showAddQuestionDialog,
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Consumer<AdminProvider>(
        builder: (context, adminProvider, child) {
          return Column(
            children: [
              // Filters
              Container(
                padding: const EdgeInsets.all(16),
                color: AppTheme.cardDark,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value:
                                adminProvider.selectedQuizCategory.isEmpty
                                    ? 'All'
                                    : adminProvider.selectedQuizCategory,
                            decoration: const InputDecoration(
                              labelText: 'Category',
                              labelStyle: TextStyle(color: AppTheme.textGray),
                              border: OutlineInputBorder(),
                            ),
                            dropdownColor: AppTheme.cardDark,
                            style: const TextStyle(color: Colors.white),
                            items:
                                _categories.map((category) {
                                  return DropdownMenuItem(
                                    value: category,
                                    child: Text(category),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              adminProvider.setQuizFilters(
                                category: value == 'All' ? '' : value,
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value:
                                adminProvider.selectedQuizDifficulty.isEmpty
                                    ? 'All'
                                    : adminProvider.selectedQuizDifficulty,
                            decoration: const InputDecoration(
                              labelText: 'Difficulty',
                              labelStyle: TextStyle(color: AppTheme.textGray),
                              border: OutlineInputBorder(),
                            ),
                            dropdownColor: AppTheme.cardDark,
                            style: const TextStyle(color: Colors.white),
                            items:
                                _difficulties.map((difficulty) {
                                  return DropdownMenuItem(
                                    value: difficulty,
                                    child: Text(difficulty),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              adminProvider.setQuizFilters(
                                difficulty: value == 'All' ? '' : value,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Questions List
              Expanded(
                child:
                    adminProvider.isLoading
                        ? const Center(
                          child: CircularProgressIndicator(
                            color: AppTheme.primaryRed,
                          ),
                        )
                        : adminProvider.hasError
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error, size: 64, color: Colors.red),
                              const SizedBox(height: 16),
                              Text(
                                'Error loading questions',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(color: Colors.white),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                adminProvider.errorMessage ?? 'Unknown error',
                                style: const TextStyle(
                                  color: AppTheme.textGray,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadQuizQuestions,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryRed,
                                ),
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                        : adminProvider.quizQuestions.isEmpty
                        ? const Center(
                          child: Text(
                            'No quiz questions found',
                            style: TextStyle(color: AppTheme.textGray),
                          ),
                        )
                        : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: adminProvider.quizQuestions.length,
                          itemBuilder: (context, index) {
                            final question = adminProvider.quizQuestions[index];
                            return Container(
                              margin: const EdgeInsets.only(bottom: 16),
                              child: Card(
                                color: AppTheme.cardDark,
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              question.question,
                                              style: Theme.of(
                                                context,
                                              ).textTheme.titleMedium?.copyWith(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                          PopupMenuButton<String>(
                                            color: AppTheme.cardDark,
                                            onSelected: (value) {
                                              if (value == 'edit') {
                                                _showEditQuestionDialog(
                                                  question,
                                                );
                                              } else if (value == 'delete') {
                                                _deleteQuestion(question);
                                              }
                                            },
                                            itemBuilder:
                                                (context) => [
                                                  const PopupMenuItem(
                                                    value: 'edit',
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons.edit,
                                                          color: Colors.white,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          'Edit',
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const PopupMenuItem(
                                                    value: 'delete',
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons.delete,
                                                          color: Colors.red,
                                                        ),
                                                        SizedBox(width: 8),
                                                        Text(
                                                          'Delete',
                                                          style: TextStyle(
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 12),

                                      // Options
                                      ...question.options.asMap().entries.map((
                                        entry,
                                      ) {
                                        final index = entry.key;
                                        final option = entry.value;
                                        final isCorrect =
                                            option == question.correctAnswer;

                                        return Container(
                                          margin: const EdgeInsets.only(
                                            bottom: 8,
                                          ),
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color:
                                                isCorrect
                                                    ? Colors.green.withValues(
                                                      alpha: 0.2,
                                                    )
                                                    : AppTheme.backgroundBlack,
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            border: Border.all(
                                              color:
                                                  isCorrect
                                                      ? Colors.green
                                                      : AppTheme.textGray
                                                          .withValues(
                                                            alpha: 0.3,
                                                          ),
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Text(
                                                '${String.fromCharCode(65 + index)}. ',
                                                style: TextStyle(
                                                  color:
                                                      isCorrect
                                                          ? Colors.green
                                                          : Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Expanded(
                                                child: Text(
                                                  option,
                                                  style: TextStyle(
                                                    color:
                                                        isCorrect
                                                            ? Colors.green
                                                            : Colors.white,
                                                  ),
                                                ),
                                              ),
                                              if (isCorrect)
                                                const Icon(
                                                  Icons.check_circle,
                                                  color: Colors.green,
                                                  size: 20,
                                                ),
                                            ],
                                          ),
                                        );
                                      }),

                                      const SizedBox(height: 12),

                                      // Metadata
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: AppTheme.primaryRed
                                                  .withValues(alpha: 0.2),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              question.category,
                                              style: TextStyle(
                                                color: AppTheme.primaryRed,
                                                fontSize: 12,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: _getDifficultyColor(
                                                question.difficulty,
                                              ).withValues(alpha: 0.2),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              question.difficulty,
                                              style: TextStyle(
                                                color: _getDifficultyColor(
                                                  question.difficulty,
                                                ),
                                                fontSize: 12,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          );
        },
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return AppTheme.textGray;
    }
  }
}

class AddQuestionDialog extends StatefulWidget {
  final AdminQuizQuestion? question;

  const AddQuestionDialog({super.key, this.question});

  @override
  State<AddQuestionDialog> createState() => _AddQuestionDialogState();
}

class _AddQuestionDialogState extends State<AddQuestionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _optionControllers = List.generate(
    4,
    (index) => TextEditingController(),
  );

  String _selectedCategory = 'Weapons';
  String _selectedDifficulty = 'Easy';
  int _correctAnswerIndex = 0;

  final List<String> _categories = [
    'Weapons',
    'Maps',
    'Characters',
    'Gameplay',
    'General',
  ];
  final List<String> _difficulties = ['Easy', 'Medium', 'Hard'];

  @override
  void initState() {
    super.initState();
    if (widget.question != null) {
      _questionController.text = widget.question!.question;
      for (int i = 0; i < widget.question!.options.length; i++) {
        _optionControllers[i].text = widget.question!.options[i];
      }
      _selectedCategory = widget.question!.category;
      _selectedDifficulty = widget.question!.difficulty;
      _correctAnswerIndex = widget.question!.options.indexOf(
        widget.question!.correctAnswer,
      );
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    for (final controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _saveQuestion() async {
    if (!_formKey.currentState!.validate()) return;

    final options = _optionControllers.map((c) => c.text.trim()).toList();
    final correctAnswer = options[_correctAnswerIndex];

    final question = AdminQuizQuestion(
      id: widget.question?.id ?? '',
      question: _questionController.text.trim(),
      options: options,
      correctAnswer: correctAnswer,
      category: _selectedCategory,
      difficulty: _selectedDifficulty,
      createdAt: widget.question?.createdAt ?? DateTime.now(),
      updatedAt: widget.question != null ? DateTime.now() : null,
    );

    final adminProvider = Provider.of<AdminProvider>(context, listen: false);

    if (widget.question != null) {
      await adminProvider.updateQuizQuestion(question);
    } else {
      await adminProvider.addQuizQuestion(question);
    }

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppTheme.cardDark,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.question != null ? 'Edit Question' : 'Add New Question',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Question
                      TextFormField(
                        controller: _questionController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          labelText: 'Question',
                          labelStyle: TextStyle(color: AppTheme.textGray),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a question';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Options
                      ...List.generate(4, (index) {
                        return Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          child: Row(
                            children: [
                              Radio<int>(
                                value: index,
                                groupValue: _correctAnswerIndex,
                                onChanged: (value) {
                                  setState(() {
                                    _correctAnswerIndex = value!;
                                  });
                                },
                                activeColor: AppTheme.primaryRed,
                              ),
                              Expanded(
                                child: TextFormField(
                                  controller: _optionControllers[index],
                                  style: const TextStyle(color: Colors.white),
                                  decoration: InputDecoration(
                                    labelText:
                                        'Option ${String.fromCharCode(65 + index)}',
                                    labelStyle: const TextStyle(
                                      color: AppTheme.textGray,
                                    ),
                                    border: const OutlineInputBorder(),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'Please enter option ${String.fromCharCode(65 + index)}';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      }),

                      const SizedBox(height: 16),

                      // Category and Difficulty
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedCategory,
                              decoration: const InputDecoration(
                                labelText: 'Category',
                                labelStyle: TextStyle(color: AppTheme.textGray),
                                border: OutlineInputBorder(),
                              ),
                              dropdownColor: AppTheme.cardDark,
                              style: const TextStyle(color: Colors.white),
                              items:
                                  _categories.map((category) {
                                    return DropdownMenuItem(
                                      value: category,
                                      child: Text(category),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedCategory = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedDifficulty,
                              decoration: const InputDecoration(
                                labelText: 'Difficulty',
                                labelStyle: TextStyle(color: AppTheme.textGray),
                                border: OutlineInputBorder(),
                              ),
                              dropdownColor: AppTheme.cardDark,
                              style: const TextStyle(color: Colors.white),
                              items:
                                  _difficulties.map((difficulty) {
                                    return DropdownMenuItem(
                                      value: difficulty,
                                      child: Text(difficulty),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedDifficulty = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Consumer<AdminProvider>(
                      builder: (context, adminProvider, child) {
                        return CustomButton(
                          text: widget.question != null ? 'Update' : 'Add',
                          onPressed:
                              adminProvider.isLoading ? null : _saveQuestion,
                          isLoading: adminProvider.isLoading,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
