import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../auth/login_screen.dart';

import '../admin/admin_panel_screen.dart';
import '../wallet/enhanced_wallet_screen.dart';
import 'payment_settings_screen.dart';
import '../wallet/withdrawal_history_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Header
            _buildProfileHeader(context),

            const SizedBox(height: 30),

            // Stats Cards
            _buildStatsSection(context),

            const SizedBox(height: 30),

            // Menu Items
            _buildMenuSection(context),

            const SizedBox(height: 30),

            // Sign Out Button
            _buildSignOutButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.user;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: AppTheme.cardDecoration,
          child: Column(
            children: [
              // Avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppTheme.primaryGradient,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryRed.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(Icons.person, color: Colors.white, size: 40),
              ),

              const SizedBox(height: 16),

              // Name
              Text(
                user?.name ?? 'User',
                style: Theme.of(context).textTheme.headlineSmall,
              ),

              const SizedBox(height: 4),

              // Email
              Text(
                user?.email ?? '',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: AppTheme.textGray),
              ),

              const SizedBox(height: 16),

              // Points
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  gradient: AppTheme.goldGradient,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.stars, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${user?.points ?? 0} Points',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsSection(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final user = userProvider.user;

        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Total Spins',
                '${user?.totalSpins ?? 0}',
                Icons.casino,
                AppTheme.primaryRed,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                'Games Played',
                '${user?.totalGamesPlayed ?? 0}',
                Icons.games,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                'Quiz Answers',
                '${user?.totalQuizAnswered ?? 0}',
                Icons.quiz,
                Colors.purple,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardDark,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textGray),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context) {
    return Column(
      children: [
        _buildMenuItem(
          context,
          'My Wallet',
          'View balance & redeem rewards',
          Icons.account_balance_wallet,
          () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const EnhancedWalletScreen()),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          context,
          'Avatar Builder',
          'Customize your avatar',
          Icons.face,
          () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('Coming Soon!')));
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          context,
          'Payment Settings',
          'Setup UPI ID & Game ID',
          Icons.payment,
          () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const PaymentSettingsScreen()),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          context,
          'Withdrawal History',
          'View your withdrawal requests',
          Icons.history,
          () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => const WithdrawalHistoryScreen(),
              ),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          context,
          'Referral Code',
          'Share and earn points',
          Icons.share,
          () {
            _showReferralDialog(context);
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          context,
          'Settings',
          'App preferences',
          Icons.settings,
          () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('Coming Soon!')));
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          context,
          'Help & Support',
          'Get help and contact us',
          Icons.help,
          () {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('Coming Soon!')));
          },
        ),

        // Secret Admin Access (only show for admin email)
        Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final user = userProvider.user;
            if (user != null &&
                (user.email == AppConstants.adminEmail ||
                    user.email == '<EMAIL>')) {
              return Column(
                children: [
                  const SizedBox(height: 12),
                  _buildMenuItem(
                    context,
                    'Admin Panel',
                    'Full administrative access',
                    Icons.admin_panel_settings,
                    () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => const AdminPanelScreen(),
                      ),
                    ),
                  ),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: AppTheme.cardDecoration,
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.primaryRed.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppTheme.primaryRed, size: 24),
        ),
        title: Text(title, style: Theme.of(context).textTheme.titleMedium),
        subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.textGray,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildSignOutButton(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return CustomButton(
          text: 'Sign Out',
          onPressed: () async {
            await authProvider.signOut();
            if (context.mounted) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (_) => const LoginScreen()),
                (route) => false,
              );
            }
          },
          backgroundColor: Colors.red.withOpacity(0.2),
          textColor: Colors.red,
          icon: Icons.logout,
        );
      },
    );
  }

  void _showReferralDialog(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final referralCode = userProvider.user?.referralCode ?? '';

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('Your Referral Code'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryRed.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    referralCode,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppTheme.primaryRed,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Share this code with friends to earn 100 points when they sign up!',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: AppTheme.textGray),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
              TextButton(
                onPressed: () {
                  // TODO: Implement share functionality
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Share functionality coming soon!'),
                    ),
                  );
                },
                child: const Text('Share'),
              ),
            ],
          ),
    );
  }
}
