import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../services/firebase_service.dart';

class PaymentSettingsScreen extends StatefulWidget {
  const PaymentSettingsScreen({super.key});

  @override
  State<PaymentSettingsScreen> createState() => _PaymentSettingsScreenState();
}

class _PaymentSettingsScreenState extends State<PaymentSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _upiController = TextEditingController();
  final _gameIdController = TextEditingController();
  final _phoneController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _upiController.dispose();
    _gameIdController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final user = Provider.of<UserProvider>(context, listen: false).user;
    if (user != null) {
      _upiController.text = user.upiId ?? '';
      _gameIdController.text = user.gameId ?? '';
      _phoneController.text = user.phoneNumber ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Payment Settings',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryRed,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.payment,
                            color: AppTheme.accentGold,
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Payment Information',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Add your payment details to withdraw points as rupees or diamonds.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // UPI Section
                _buildSectionCard(
                  'UPI Details',
                  'For withdrawing rupees to your UPI account',
                  Icons.account_balance_wallet,
                  AppTheme.successGreen,
                  [
                    _buildTextField(
                      controller: _upiController,
                      label: 'UPI ID',
                      hint: 'example@paytm, **********@ybl',
                      icon: Icons.payment,
                      validator: _validateUPI,
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Game ID Section
                _buildSectionCard(
                  'Free Fire Game ID',
                  'For receiving diamonds in Free Fire',
                  Icons.games,
                  AppTheme.accentGold,
                  [
                    _buildTextField(
                      controller: _gameIdController,
                      label: 'Free Fire Game ID',
                      hint: 'Enter your 9-12 digit Game ID',
                      icon: Icons.videogame_asset,
                      validator: _validateGameId,
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Phone Number Section
                _buildSectionCard(
                  'Contact Information',
                  'For verification and support',
                  Icons.phone,
                  Colors.blue,
                  [
                    _buildTextField(
                      controller: _phoneController,
                      label: 'Phone Number',
                      hint: '+91 **********',
                      icon: Icons.phone,
                      validator: _validatePhone,
                      keyboardType: TextInputType.phone,
                    ),
                  ],
                ),

                const SizedBox(height: 30),

                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: CustomButton(
                    text: _isLoading ? 'Saving...' : 'Save Payment Details',
                    icon: _isLoading ? null : Icons.save,
                    onPressed: _isLoading ? null : _savePaymentDetails,
                    backgroundColor: AppTheme.accentGold,
                    textColor: Colors.black,
                  ),
                ),

                const SizedBox(height: 20),

                // Info Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.info, color: Colors.blue, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Important Information',
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildInfoItem(
                        '• UPI ID is required for rupee withdrawals',
                      ),
                      _buildInfoItem(
                        '• Game ID is required for diamond transfers',
                      ),
                      _buildInfoItem('• Phone number is used for verification'),
                      _buildInfoItem(
                        '• All information is kept secure and private',
                      ),
                      _buildInfoItem('• You can update these details anytime'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard(
    String title,
    String description,
    IconData icon,
    Color color,
    List<Widget> children,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      description,
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[400]),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppTheme.accentGold),
        labelStyle: const TextStyle(color: AppTheme.accentGold),
        hintStyle: const TextStyle(color: Colors.grey),
        filled: true,
        fillColor: AppTheme.cardDark.withValues(alpha: 0.5),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppTheme.accentGold),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppTheme.accentGold),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppTheme.accentGold, width: 2),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: Colors.grey[300]),
      ),
    );
  }

  String? _validateUPI(String? value) {
    if (value == null || value.isEmpty) {
      return null; // UPI is optional
    }

    // Basic UPI validation
    if (!value.contains('@')) {
      return 'Invalid UPI ID format';
    }

    final parts = value.split('@');
    if (parts.length != 2 || parts[0].isEmpty || parts[1].isEmpty) {
      return 'Invalid UPI ID format';
    }

    return null;
  }

  String? _validateGameId(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Game ID is optional
    }

    // Free Fire Game ID validation (9-12 digits)
    if (!RegExp(r'^\d{9,12}$').hasMatch(value)) {
      return 'Game ID must be 9-12 digits';
    }

    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }

    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');

    // Indian phone number validation
    if (!RegExp(r'^(\+91)?[6-9]\d{9}$').hasMatch(cleanPhone)) {
      return 'Enter a valid Indian phone number';
    }

    return null;
  }

  Future<void> _savePaymentDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.user;

      if (user == null) {
        throw Exception('User not found');
      }

      // Update user with payment details
      final updatedUser = user.copyWith(
        upiId:
            _upiController.text.trim().isEmpty
                ? null
                : _upiController.text.trim(),
        gameId:
            _gameIdController.text.trim().isEmpty
                ? null
                : _gameIdController.text.trim(),
        phoneNumber:
            _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
      );

      // Save to Firestore
      await FirebaseService.updateUserProfile(updatedUser);

      // Update local state
      userProvider.setUser(updatedUser);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Payment details saved successfully!'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
