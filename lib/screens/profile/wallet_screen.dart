import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/wallet_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/wallet_model.dart';
import '../../models/user_model.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  @override
  void initState() {
    super.initState();
    _loadWalletData();
  }

  void _loadWalletData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final walletProvider = Provider.of<WalletProvider>(
        context,
        listen: false,
      );

      if (userProvider.user != null) {
        walletProvider.loadWallet(userProvider.user!.uid);
        walletProvider.loadRedemptionHistory(userProvider.user!.uid);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundBlack,
      appBar: AppBar(
        title: const Text('My Wallet'),
        backgroundColor: AppTheme.backgroundBlack,
        elevation: 0,
      ),
      body: Consumer2<UserProvider, WalletProvider>(
        builder: (context, userProvider, walletProvider, child) {
          final user = userProvider.user;

          if (user == null) {
            return const Center(
              child: Text('Please log in to view your wallet'),
            );
          }

          if (walletProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Wallet Balance Card
                _buildBalanceCard(user, walletProvider),

                const SizedBox(height: 24),

                // Conversion Rates
                _buildConversionRates(walletProvider),

                const SizedBox(height: 24),

                // Action Buttons
                _buildActionButtons(context, user, walletProvider),

                const SizedBox(height: 24),

                // Recent Redemptions
                _buildRecentRedemptions(walletProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBalanceCard(UserModel user, WalletProvider walletProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryRed.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'Wallet Balance',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Points Balance
          Row(
            children: [
              const Icon(Icons.stars, color: AppTheme.accentGold, size: 24),
              const SizedBox(width: 8),
              Text(
                '${user.points} Points',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Diamonds Balance
          Row(
            children: [
              const Icon(Icons.diamond, color: AppTheme.accentGold, size: 24),
              const SizedBox(width: 8),
              Text(
                '${user.diamonds} Diamonds',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(color: Colors.white),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Conversion Preview
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Can redeem for:',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '₹${walletProvider.pointsToRupees(user.points)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppTheme.accentGold,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${walletProvider.pointsToDiamonds(user.points)} 💎',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppTheme.accentGold,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversionRates(WalletProvider walletProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Conversion Rates',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildRateCard(
                  '${AppConstants.pointsToRupeesRate} Points',
                  '₹1',
                  Icons.currency_rupee,
                  AppTheme.successGreen,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildRateCard(
                  '${AppConstants.pointsToDiamondsRate} Points',
                  '1 💎',
                  Icons.diamond,
                  AppTheme.accentGold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRateCard(
    String points,
    String reward,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            points,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textGray),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            reward,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    UserModel user,
    WalletProvider walletProvider,
  ) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Redeem Rupees',
                icon: Icons.currency_rupee,
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Rupee redemption coming soon!'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                },
                backgroundColor: AppTheme.successGreen,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: CustomButton(
                text: 'Redeem Diamonds',
                icon: Icons.diamond,
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Diamond redemption coming soon!'),
                      backgroundColor: AppTheme.accentGold,
                    ),
                  );
                },
                backgroundColor: AppTheme.accentGold,
                textColor: Colors.black,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        CustomButton(
          text: 'Redemption History',
          icon: Icons.history,
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Redemption history coming soon!')),
            );
          },
          backgroundColor: AppTheme.cardDark,
          width: double.infinity,
        ),
      ],
    );
  }

  Widget _buildRecentRedemptions(WalletProvider walletProvider) {
    final recentRedemptions = walletProvider.redemptionHistory.take(3).toList();

    if (recentRedemptions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: AppTheme.cardDecoration,
        child: Column(
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: AppTheme.textGray.withOpacity(0.5),
            ),
            const SizedBox(height: 12),
            Text(
              'No redemptions yet',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: AppTheme.textGray),
            ),
            const SizedBox(height: 8),
            Text(
              'Start earning points and redeem for rewards!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textGray.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Redemptions',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('View all redemptions coming soon!'),
                    ),
                  );
                },
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 12),

          ...recentRedemptions.map(
            (redemption) => _buildRedemptionTile(redemption),
          ),
        ],
      ),
    );
  }

  Widget _buildRedemptionTile(RedemptionRequest redemption) {
    Color statusColor;
    switch (redemption.status) {
      case RedemptionStatus.approved:
        statusColor = AppTheme.successGreen;
        break;
      case RedemptionStatus.rejected:
        statusColor = Colors.red;
        break;
      case RedemptionStatus.processing:
        statusColor = Colors.orange;
        break;
      default:
        statusColor = AppTheme.textGray;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.backgroundBlack.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            redemption.type == RedemptionType.rupees
                ? Icons.currency_rupee
                : Icons.diamond,
            color:
                redemption.type == RedemptionType.rupees
                    ? AppTheme.successGreen
                    : AppTheme.accentGold,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  redemption.type == RedemptionType.rupees
                      ? '₹${redemption.amount}'
                      : '${redemption.amount} Diamonds',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${redemption.pointsUsed} points used',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: AppTheme.textGray),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              redemption.statusDisplayName,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
