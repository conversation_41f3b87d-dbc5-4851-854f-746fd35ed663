import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/banner_ad_widget.dart';
import '../../services/navigation_service.dart';
import '../../services/firebase_service.dart';
import '../../models/user_model.dart';

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<LeaderboardEntry> _dailyLeaderboard = [];
  List<LeaderboardEntry> _weeklyLeaderboard = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadLeaderboards();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadLeaderboards() async {
    setState(() => _isLoading = true);
    
    try {
      // Load daily and weekly leaderboards
      final dailyData = await FirebaseService.getLeaderboard('daily');
      final weeklyData = await FirebaseService.getLeaderboard('weekly');
      
      _dailyLeaderboard = _parseLeaderboardData(dailyData);
      _weeklyLeaderboard = _parseLeaderboardData(weeklyData);
      
      // Sort by points (descending)
      _dailyLeaderboard.sort((a, b) => b.points.compareTo(a.points));
      _weeklyLeaderboard.sort((a, b) => b.points.compareTo(a.points));
      
    } catch (e) {
      debugPrint('Error loading leaderboards: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  List<LeaderboardEntry> _parseLeaderboardData(Map<String, dynamic> data) {
    final entries = <LeaderboardEntry>[];
    
    data.forEach((userId, userData) {
      if (userData is Map<String, dynamic>) {
        entries.add(LeaderboardEntry(
          userId: userId,
          name: userData['name'] ?? 'Unknown',
          points: userData['points'] ?? 0,
          avatar: userData['avatar'] ?? {},
        ));
      }
    });
    
    return entries;
  }

  @override
  Widget build(BuildContext context) {
    return AdAwareWillPopScope(
      child: Scaffold(
        backgroundColor: AppTheme.backgroundBlack,
        appBar: AppBar(
          title: const Text(
            'Leaderboard',
            style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
          ),
          backgroundColor: AppTheme.primaryRed,
          elevation: 0,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: AppTheme.accentGold,
            labelColor: AppTheme.accentGold,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(text: 'Daily'),
              Tab(text: 'Weekly'),
            ],
          ),
        ),
        body: Container(
          decoration: AppTheme.backgroundDecoration,
          child: Column(
            children: [
              // Banner Ad
              const BannerAdWidget(),
              
              // Current User Rank
              Consumer<UserProvider>(
                builder: (context, userProvider, child) {
                  final user = userProvider.user;
                  if (user == null) return const SizedBox.shrink();
                  
                  return Container(
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(16),
                    decoration: AppTheme.cardDecoration,
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: AppTheme.accentGold,
                          child: Text(
                            user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                            style: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '${user.points} points',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppTheme.accentGold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppTheme.accentGold,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Your Rank',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              
              // Leaderboard Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildLeaderboardList(_dailyLeaderboard, 'daily'),
                    _buildLeaderboardList(_weeklyLeaderboard, 'weekly'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeaderboardList(List<LeaderboardEntry> entries, String type) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentGold),
        ),
      );
    }

    if (entries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.leaderboard,
              size: 64,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'No ${type} rankings yet',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Be the first to earn points!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadLeaderboards,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: entries.length,
        itemBuilder: (context, index) {
          final entry = entries[index];
          final rank = index + 1;
          
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: AppTheme.cardDecoration.copyWith(
              border: rank <= 3 ? Border.all(
                color: _getRankColor(rank),
                width: 2,
              ) : null,
            ),
            child: Row(
              children: [
                // Rank
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getRankColor(rank),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: rank <= 3 
                        ? Icon(
                            _getRankIcon(rank),
                            color: Colors.white,
                            size: 20,
                          )
                        : Text(
                            '$rank',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        entry.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${entry.points} points',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.accentGold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Trophy for top 3
                if (rank <= 3)
                  Icon(
                    Icons.emoji_events,
                    color: _getRankColor(rank),
                    size: 24,
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return const Color(0xFFFFD700); // Gold
      case 2:
        return const Color(0xFFC0C0C0); // Silver
      case 3:
        return const Color(0xFFCD7F32); // Bronze
      default:
        return AppTheme.primaryRed;
    }
  }

  IconData _getRankIcon(int rank) {
    switch (rank) {
      case 1:
        return Icons.looks_one;
      case 2:
        return Icons.looks_two;
      case 3:
        return Icons.looks_3;
      default:
        return Icons.person;
    }
  }
}

class LeaderboardEntry {
  final String userId;
  final String name;
  final int points;
  final Map<String, dynamic> avatar;

  LeaderboardEntry({
    required this.userId,
    required this.name,
    required this.points,
    required this.avatar,
  });
}
