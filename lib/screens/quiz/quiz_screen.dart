import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../models/quiz_model.dart';

class QuizScreen extends StatelessWidget {
  const QuizScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Free Fire Quiz'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: Safe<PERSON>rea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Your Knowledge',
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Answer Free Fire questions and earn points!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Quiz Categories
                Text(
                  'Categories',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),

                Expanded(
                  child: ListView.builder(
                    itemCount: AppConstants.tipsCategories.length,
                    itemBuilder: (context, index) {
                      final category = AppConstants.tipsCategories[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _buildCategoryCard(context, category, index),
                      );
                    },
                  ),
                ),

                const SizedBox(height: 20),

                // Rules
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.quiz, color: AppTheme.accentGold),
                          const SizedBox(width: 8),
                          Text(
                            'Quiz Rules',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: AppTheme.accentGold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '• 10 points per correct answer\n'
                        '• 5 questions per level\n'
                        '• 15 seconds per question\n'
                        '• Watch ads after every 3 levels for bonus points',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(BuildContext context, String category, int index) {
    final icons = [
      Icons.sports_esports,
      Icons.map,
      Icons.person,
      Icons.psychology,
      Icons.inventory,
    ];

    final colors = [
      Colors.red,
      Colors.green,
      Colors.blue,
      Colors.purple,
      Colors.orange,
    ];

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colors[index % colors.length],
            colors[index % colors.length].withOpacity(0.7),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: colors[index % colors.length].withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icons[index % icons.length],
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Text(
          category,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'Level ${index + 1} • 5 Questions',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.white.withOpacity(0.8)),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white,
          size: 16,
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (_) => QuizGameScreen(category: category, level: index + 1),
            ),
          );
        },
      ),
    );
  }
}

class QuizGameScreen extends StatefulWidget {
  final String category;
  final int level;

  const QuizGameScreen({Key? key, required this.category, required this.level})
    : super(key: key);

  @override
  State<QuizGameScreen> createState() => _QuizGameScreenState();
}

class _QuizGameScreenState extends State<QuizGameScreen>
    with TickerProviderStateMixin {
  late AnimationController _timerController;
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _timeLeft = 15;
  String? _selectedAnswer;
  bool _showResult = false;

  // Sample questions (in a real app, these would come from Firebase)
  late List<QuizQuestion> _questions;

  @override
  void initState() {
    super.initState();
    _initializeQuestions();
    _timerController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );
    _startTimer();
  }

  void _initializeQuestions() {
    // Use sample questions for now
    _questions =
        QuizData.sampleQuestions
            .map(
              (data) => QuizQuestion(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                question: data['question'],
                options: List<String>.from(data['options']),
                correctAnswer: data['correctAnswer'],
                category: data['category'],
                difficulty: data['difficulty'],
                explanation: data['explanation'],
              ),
            )
            .take(5)
            .toList();
  }

  void _startTimer() {
    _timerController.forward().then((_) {
      if (!_showResult) {
        _nextQuestion();
      }
    });
  }

  void _selectAnswer(String answer) {
    if (_showResult) return;

    setState(() {
      _selectedAnswer = answer;
      _showResult = true;
    });

    _timerController.stop();

    if (answer == _questions[_currentQuestionIndex].correctAnswer) {
      setState(() {
        _score++;
      });
    }

    Future.delayed(const Duration(seconds: 2), () {
      _nextQuestion();
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _selectedAnswer = null;
        _showResult = false;
        _timeLeft = 15;
      });
      _timerController.reset();
      _startTimer();
    } else {
      _finishQuiz();
    }
  }

  void _finishQuiz() {
    final pointsEarned = _score * 10;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.cardDark,
            title: const Text('Quiz Complete!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Score: $_score/${_questions.length}'),
                Text('Points Earned: $pointsEarned'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                child: const Text('Continue'),
              ),
            ],
          ),
    );
  }

  @override
  void dispose() {
    _timerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_questions.isEmpty) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final question = _questions[_currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.category} - Level ${widget.level}'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Progress and Timer
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: (_currentQuestionIndex + 1) / _questions.length,
                        backgroundColor: AppTheme.textGray.withOpacity(0.3),
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryRed,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    AnimatedBuilder(
                      animation: _timerController,
                      builder: (context, child) {
                        return Text(
                          '${15 - (_timerController.value * 15).round()}s',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(color: AppTheme.primaryRed),
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Question Number
                Text(
                  'Question ${_currentQuestionIndex + 1}/${_questions.length}',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: AppTheme.textGray),
                ),

                const SizedBox(height: 20),

                // Question
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: AppTheme.cardDecoration,
                  child: Text(
                    question.question,
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 30),

                // Options
                Expanded(
                  child: ListView.builder(
                    itemCount: question.options.length,
                    itemBuilder: (context, index) {
                      final option = question.options[index];
                      final isSelected = _selectedAnswer == option;
                      final isCorrect = option == question.correctAnswer;

                      Color backgroundColor = AppTheme.cardDark;
                      if (_showResult) {
                        if (isCorrect) {
                          backgroundColor = Colors.green;
                        } else if (isSelected && !isCorrect) {
                          backgroundColor = Colors.red;
                        }
                      }

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: GestureDetector(
                          onTap: () => _selectAnswer(option),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color:
                                    isSelected
                                        ? AppTheme.primaryRed
                                        : Colors.transparent,
                                width: 2,
                              ),
                            ),
                            child: Text(
                              option,
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
