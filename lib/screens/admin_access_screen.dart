import 'package:flutter/material.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import 'admin/admin_login_screen.dart';

class AdminAccessScreen extends StatefulWidget {
  const AdminAccessScreen({super.key});

  @override
  State<AdminAccessScreen> createState() => _AdminAccessScreenState();
}

class _AdminAccessScreenState extends State<AdminAccessScreen> {
  int _tapCount = 0;
  static const int _requiredTaps = 7;

  void _onLogoTap() {
    setState(() {
      _tapCount++;
    });

    if (_tapCount >= _requiredTaps) {
      _navigateToAdminLogin();
    } else {
      // Show hint after 3 taps
      if (_tapCount == 3) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tap ${_requiredTaps - _tapCount} more times to access admin panel'),
            backgroundColor: AppTheme.primaryRed,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }

    // Reset counter after 5 seconds of inactivity
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _tapCount = 0;
        });
      }
    });
  }

  void _navigateToAdminLogin() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AdminLoginScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo (tappable for admin access)
                GestureDetector(
                  onTap: _onLogoTap,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: AppTheme.primaryGradient,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryRed.withValues(alpha: 0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.local_fire_department,
                      size: 80,
                      color: Colors.white,
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // App Title
                Text(
                  AppConstants.appName,
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  AppConstants.appTagline,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.textGray,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 48),
                
                // Admin Access Instructions
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppTheme.cardDark,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppTheme.primaryRed.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.admin_panel_settings,
                        size: 48,
                        color: AppTheme.primaryRed,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Admin Panel Access',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tap the logo above multiple times to access the admin panel',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textGray,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.backgroundBlack,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.email,
                              size: 16,
                              color: AppTheme.primaryRed,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Admin Email: ${AppConstants.adminEmail}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Tap Progress Indicator
                if (_tapCount > 0)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryRed.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.primaryRed.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Taps: $_tapCount / $_requiredTaps',
                          style: TextStyle(
                            color: AppTheme.primaryRed,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: _tapCount / _requiredTaps,
                          backgroundColor: AppTheme.textGray.withValues(alpha: 0.3),
                          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryRed),
                        ),
                      ],
                    ),
                  ),
                
                const Spacer(),
                
                // Footer
                Text(
                  '⚠️ This admin panel is for managing ${AppConstants.appName}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textGray,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Not affiliated with Garena or Free Fire',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textGray,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
