import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart' as rtdb;
import 'package:flutter/foundation.dart';
import '../models/admin_models.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class AdminService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final rtdb.FirebaseDatabase _database = rtdb.FirebaseDatabase.instance;

  // Admin email constant
  static const String ADMIN_EMAIL = '<EMAIL>';

  // Check if current user is admin
  static bool isAdmin() {
    final user = _auth.currentUser;
    return user != null &&
        (user.email == AppConstants.adminEmail || user.email == ADMIN_EMAIL);
  }

  // Check if user is admin by email
  static bool isAdminByEmail(String email) {
    return email == AppConstants.adminEmail || email == ADMIN_EMAIL;
  }

  // Check if user model has admin access
  static bool hasAdminAccess(UserModel user) {
    return user.hasAdminAccess || isAdminByEmail(user.email);
  }

  // Verify admin access
  static void verifyAdminAccess() {
    if (!isAdmin()) {
      throw Exception('Unauthorized: Admin access required');
    }
  }

  // Log admin actions
  static Future<void> logAdminAction({
    required String adminId,
    required String adminEmail,
    required String action,
    required String targetType,
    String? targetId,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    required String description,
  }) async {
    try {
      final log = AdminActionLog(
        id: '',
        adminId: adminId,
        adminEmail: adminEmail,
        action: action,
        targetType: targetType,
        targetId: targetId,
        oldValues: oldValues ?? {},
        newValues: newValues ?? {},
        timestamp: DateTime.now(),
        description: description,
      );

      await _firestore.collection('admin_logs').add(log.toFirestore());
    } catch (e) {
      debugPrint('Error logging admin action: $e');
    }
  }

  // Check if Firebase rules allow admin access
  static Future<bool> hasAdminPermissions() async {
    try {
      if (!isAdmin()) return false;

      // Try a simple read operation to test permissions
      await _firestore.collection('users').limit(1).get();
      return true;
    } catch (e) {
      debugPrint('Admin permissions check failed: $e');
      return false;
    }
  }

  // APP SETTINGS MANAGEMENT
  static Future<AppSettings?> getAppSettings() async {
    verifyAdminAccess();

    try {
      final doc = await _firestore.collection('app_settings').doc('main').get();
      if (doc.exists) {
        return AppSettings.fromFirestore(doc);
      }

      // Create default settings if none exist
      final defaultSettings = AppSettings(
        id: 'main',
        pointsToRupeesRate: 100.0,
        pointsToDiamondsRate: 50.0,
        dailyLoginBasePoints: 50,
        spinCooldownHours: 24,
        maxDailyChests: 5,
        lastUpdated: DateTime.now(),
        updatedBy: _auth.currentUser?.email ?? '',
      );

      await _firestore
          .collection('app_settings')
          .doc('main')
          .set(defaultSettings.toFirestore());
      return defaultSettings;
    } catch (e) {
      debugPrint('Error getting app settings: $e');
      return null;
    }
  }

  static Future<void> updateAppSettings(AppSettings settings) async {
    verifyAdminAccess();

    try {
      final oldSettings = await getAppSettings();
      final adminEmail = _auth.currentUser?.email ?? '';

      final updatedSettings = settings.copyWith(
        lastUpdated: DateTime.now(),
        updatedBy: adminEmail,
      );

      await _firestore
          .collection('app_settings')
          .doc('main')
          .set(updatedSettings.toFirestore());

      await logAdminAction(
        adminId: _auth.currentUser?.uid ?? '',
        adminEmail: adminEmail,
        action: 'UPDATE_SETTINGS',
        targetType: 'settings',
        targetId: 'main',
        oldValues: oldSettings?.toFirestore(),
        newValues: updatedSettings.toFirestore(),
        description: 'Updated app settings',
      );

      debugPrint('App settings updated successfully');
    } catch (e) {
      debugPrint('Error updating app settings: $e');
      rethrow;
    }
  }

  static Future<void> updateConversionRates({
    double? pointsToRupeesRate,
    double? pointsToDiamondsRate,
  }) async {
    verifyAdminAccess();

    final currentSettings = await getAppSettings();
    if (currentSettings == null) return;

    final updatedSettings = currentSettings.copyWith(
      pointsToRupeesRate: pointsToRupeesRate,
      pointsToDiamondsRate: pointsToDiamondsRate,
    );

    await updateAppSettings(updatedSettings);
  }

  static Future<void> toggleMaintenanceMode(
    bool enabled,
    String? message,
  ) async {
    verifyAdminAccess();

    final currentSettings = await getAppSettings();
    if (currentSettings == null) return;

    final updatedSettings = currentSettings.copyWith(
      maintenanceMode: enabled,
      maintenanceMessage: message ?? '',
    );

    await updateAppSettings(updatedSettings);
  }

  // QUIZ MANAGEMENT
  static Future<List<AdminQuizQuestion>> getQuizQuestions({
    String? category,
    String? difficulty,
  }) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.quizCollection);

    if (category != null && category.isNotEmpty) {
      query = query.where('category', isEqualTo: category);
    }

    if (difficulty != null && difficulty.isNotEmpty) {
      query = query.where('difficulty', isEqualTo: difficulty);
    }

    final snapshot = await query.orderBy('createdAt', descending: true).get();
    return snapshot.docs
        .map((doc) => AdminQuizQuestion.fromFirestore(doc))
        .toList();
  }

  static Future<void> addQuizQuestion(AdminQuizQuestion question) async {
    verifyAdminAccess();

    final questionData = question.toFirestore();
    questionData['createdAt'] = FieldValue.serverTimestamp();

    await _firestore.collection(AppConstants.quizCollection).add(questionData);
    debugPrint('Quiz question added successfully');
  }

  static Future<void> updateQuizQuestion(AdminQuizQuestion question) async {
    verifyAdminAccess();

    final questionData = question.toFirestore();
    questionData['updatedAt'] = FieldValue.serverTimestamp();

    await _firestore
        .collection(AppConstants.quizCollection)
        .doc(question.id)
        .update(questionData);
    debugPrint('Quiz question updated successfully');
  }

  static Future<void> deleteQuizQuestion(String questionId) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.quizCollection)
        .doc(questionId)
        .delete();
    debugPrint('Quiz question deleted successfully');
  }

  // REDEMPTION MANAGEMENT
  static Future<List<AdminRedemptionRequest>> getRedemptionRequests({
    String status = 'all',
  }) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.redemptionsCollection);

    if (status != 'all') {
      query = query.where('status', isEqualTo: status);
    }

    final snapshot = await query.orderBy('requestDate', descending: true).get();
    return snapshot.docs
        .map((doc) => AdminRedemptionRequest.fromFirestore(doc))
        .toList();
  }

  static Future<void> updateRedemptionStatus(
    String requestId,
    String status,
    String? adminNotes,
  ) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.redemptionsCollection)
        .doc(requestId)
        .update({
          'status': status,
          'processedDate': FieldValue.serverTimestamp(),
          'adminNotes': adminNotes,
        });
    debugPrint('Redemption request updated: $status');
  }

  // TIPS MANAGEMENT
  static Future<List<AdminTip>> getTips({String? category}) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.tipsCollection);

    if (category != null && category.isNotEmpty) {
      query = query.where('category', isEqualTo: category);
    }

    final snapshot = await query.orderBy('createdAt', descending: true).get();
    return snapshot.docs.map((doc) => AdminTip.fromFirestore(doc)).toList();
  }

  static Future<void> addTip(AdminTip tip) async {
    verifyAdminAccess();

    final tipData = tip.toFirestore();
    tipData['createdAt'] = FieldValue.serverTimestamp();

    await _firestore.collection(AppConstants.tipsCollection).add(tipData);
    debugPrint('Tip added successfully');
  }

  static Future<void> updateTip(AdminTip tip) async {
    verifyAdminAccess();

    final tipData = tip.toFirestore();
    tipData['updatedAt'] = FieldValue.serverTimestamp();

    await _firestore
        .collection(AppConstants.tipsCollection)
        .doc(tip.id)
        .update(tipData);
    debugPrint('Tip updated successfully');
  }

  static Future<void> deleteTip(String tipId) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.tipsCollection)
        .doc(tipId)
        .delete();
    debugPrint('Tip deleted successfully');
  }

  // ENHANCED USER MANAGEMENT
  static Future<List<UserModel>> getAllUsers({int? limit}) async {
    verifyAdminAccess();

    try {
      Query query = _firestore
          .collection(AppConstants.usersCollection)
          .orderBy('createdAt', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error getting all users: $e');
      return [];
    }
  }

  static Future<List<AdminUserInfo>> getUsers({
    int limit = 50,
    String? searchEmail,
  }) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.usersCollection);

    if (searchEmail != null && searchEmail.isNotEmpty) {
      query = query.where('email', isEqualTo: searchEmail);
    }

    final snapshot =
        await query.orderBy('createdAt', descending: true).limit(limit).get();

    return snapshot.docs
        .map((doc) => AdminUserInfo.fromFirestore(doc))
        .toList();
  }

  static Future<List<UserModel>> searchUsers(String searchTerm) async {
    verifyAdminAccess();

    try {
      final snapshot =
          await _firestore
              .collection(AppConstants.usersCollection)
              .where('email', isGreaterThanOrEqualTo: searchTerm)
              .where('email', isLessThanOrEqualTo: searchTerm + '\uf8ff')
              .get();

      final nameSnapshot =
          await _firestore
              .collection(AppConstants.usersCollection)
              .where('name', isGreaterThanOrEqualTo: searchTerm)
              .where('name', isLessThanOrEqualTo: searchTerm + '\uf8ff')
              .get();

      final users = <UserModel>[];
      final seenIds = <String>{};

      for (final doc in [...snapshot.docs, ...nameSnapshot.docs]) {
        if (!seenIds.contains(doc.id)) {
          users.add(UserModel.fromFirestore(doc));
          seenIds.add(doc.id);
        }
      }

      return users;
    } catch (e) {
      debugPrint('Error searching users: $e');
      return [];
    }
  }

  static Future<void> updateUserStatus({
    required String userId,
    required String status,
    required bool isActive,
    String? reason,
  }) async {
    verifyAdminAccess();

    try {
      final userRef = _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId);
      final userDoc = await userRef.get();

      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      final oldUser = UserModel.fromFirestore(userDoc);
      final adminEmail = _auth.currentUser?.email ?? '';

      await userRef.update({
        'status': status,
        'isActive': isActive,
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      await logAdminAction(
        adminId: _auth.currentUser?.uid ?? '',
        adminEmail: adminEmail,
        action: 'UPDATE_USER_STATUS',
        targetType: 'user',
        targetId: userId,
        oldValues: {'status': oldUser.status, 'isActive': oldUser.isActive},
        newValues: {'status': status, 'isActive': isActive},
        description:
            'Updated user status to $status${reason != null ? ': $reason' : ''}',
      );

      debugPrint('User status updated successfully');
    } catch (e) {
      debugPrint('Error updating user status: $e');
      rethrow;
    }
  }

  static Future<void> banUser(String userId, bool isBanned) async {
    verifyAdminAccess();

    await updateUserStatus(
      userId: userId,
      status: isBanned ? 'banned' : 'active',
      isActive: !isBanned,
      reason: isBanned ? 'User banned by admin' : 'User unbanned by admin',
    );
  }

  static Future<void> updateUserPoints(String userId, int points) async {
    verifyAdminAccess();

    try {
      final userRef = _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId);
      final userDoc = await userRef.get();

      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      final oldUser = UserModel.fromFirestore(userDoc);
      final adminEmail = _auth.currentUser?.email ?? '';

      await userRef.update({
        'points': points,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await logAdminAction(
        adminId: _auth.currentUser?.uid ?? '',
        adminEmail: adminEmail,
        action: 'UPDATE_USER_POINTS',
        targetType: 'user',
        targetId: userId,
        oldValues: {'points': oldUser.points},
        newValues: {'points': points},
        description: 'Updated user points from ${oldUser.points} to $points',
      );

      debugPrint('User points updated successfully');
    } catch (e) {
      debugPrint('Error updating user points: $e');
      rethrow;
    }
  }

  static Future<void> deleteUser({
    required String userId,
    String? reason,
  }) async {
    verifyAdminAccess();

    try {
      final userRef = _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId);
      final userDoc = await userRef.get();

      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      final user = UserModel.fromFirestore(userDoc);
      final adminEmail = _auth.currentUser?.email ?? '';

      // Move user data to deleted_users collection for backup
      await _firestore.collection('deleted_users').doc(userId).set({
        ...user.toFirestore(),
        'deletedAt': FieldValue.serverTimestamp(),
        'deletedBy': adminEmail,
        'deletionReason': reason,
      });

      // Delete user
      await userRef.delete();

      await logAdminAction(
        adminId: _auth.currentUser?.uid ?? '',
        adminEmail: adminEmail,
        action: 'DELETE_USER',
        targetType: 'user',
        targetId: userId,
        oldValues: user.toFirestore(),
        newValues: {},
        description:
            'Deleted user ${user.email}${reason != null ? ': $reason' : ''}',
      );

      debugPrint('User deleted successfully');
    } catch (e) {
      debugPrint('Error deleting user: $e');
      rethrow;
    }
  }

  // ANALYTICS
  static Future<AdminAnalytics> getAnalytics() async {
    verifyAdminAccess();

    try {
      // Get analytics document or create default
      final analyticsDoc =
          await _firestore
              .collection(AppConstants.analyticsCollection)
              .doc('stats')
              .get();

      if (analyticsDoc.exists) {
        return AdminAnalytics.fromFirestore(analyticsDoc);
      } else {
        // Calculate analytics from scratch
        return await _calculateAnalytics();
      }
    } catch (e) {
      debugPrint('Error getting analytics: $e');
      return await _calculateAnalytics();
    }
  }

  static Future<AdminAnalytics> _calculateAnalytics() async {
    final now = DateTime.now();
    final oneDayAgo = now.subtract(const Duration(days: 1));
    final oneWeekAgo = now.subtract(const Duration(days: 7));

    // Get total users
    final usersSnapshot =
        await _firestore.collection(AppConstants.usersCollection).get();
    final totalUsers = usersSnapshot.docs.length;

    // Get daily active users
    final dailyActiveSnapshot =
        await _firestore
            .collection(AppConstants.usersCollection)
            .where('lastLogin', isGreaterThan: Timestamp.fromDate(oneDayAgo))
            .get();
    final dailyActiveUsers = dailyActiveSnapshot.docs.length;

    // Get weekly active users
    final weeklyActiveSnapshot =
        await _firestore
            .collection(AppConstants.usersCollection)
            .where('lastLogin', isGreaterThan: Timestamp.fromDate(oneWeekAgo))
            .get();
    final weeklyActiveUsers = weeklyActiveSnapshot.docs.length;

    // Get redemption stats
    final redemptionsSnapshot =
        await _firestore.collection(AppConstants.redemptionsCollection).get();
    final totalRedemptions = redemptionsSnapshot.docs.length;

    final pendingRedemptions =
        redemptionsSnapshot.docs
            .where(
              (doc) =>
                  (doc.data() as Map<String, dynamic>)['status'] == 'pending',
            )
            .length;

    // Calculate total points
    int totalPoints = 0;
    for (final doc in usersSnapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      totalPoints += (data['points'] as int?) ?? 0;
    }

    final analytics = AdminAnalytics(
      totalUsers: totalUsers,
      dailyActiveUsers: dailyActiveUsers,
      weeklyActiveUsers: weeklyActiveUsers,
      totalSpins: 0, // TODO: Implement spin tracking
      totalQuizzes: 0, // TODO: Implement quiz tracking
      totalRedemptions: totalRedemptions,
      pendingRedemptions: pendingRedemptions,
      totalPoints: totalPoints,
      lastUpdated: now,
    );

    // Save analytics for caching
    await _firestore
        .collection(AppConstants.analyticsCollection)
        .doc('stats')
        .set(analytics.toFirestore());

    return analytics;
  }

  // Get comprehensive dashboard statistics
  static Future<Map<String, dynamic>> getDashboardStats() async {
    verifyAdminAccess();

    try {
      final usersSnapshot =
          await _firestore.collection(AppConstants.usersCollection).get();
      final redemptionsSnapshot =
          await _firestore.collection(AppConstants.redemptionsCollection).get();

      final totalUsers = usersSnapshot.docs.length;
      final activeUsers =
          usersSnapshot.docs
              .where(
                (doc) =>
                    (doc.data()['isActive'] ?? true) &&
                    (doc.data()['status'] ?? 'active') == 'active',
              )
              .length;
      final bannedUsers =
          usersSnapshot.docs
              .where((doc) => (doc.data()['status'] ?? 'active') == 'banned')
              .length;

      final totalRedemptions = redemptionsSnapshot.docs.length;
      final pendingRedemptions =
          redemptionsSnapshot.docs
              .where((doc) => (doc.data()['status'] ?? 'pending') == 'pending')
              .length;
      final completedRedemptions =
          redemptionsSnapshot.docs
              .where(
                (doc) => (doc.data()['status'] ?? 'pending') == 'completed',
              )
              .length;

      final totalPoints = usersSnapshot.docs.fold<int>(
        0,
        (sum, doc) => sum + ((doc.data()['points'] ?? 0) as int),
      );

      return {
        'totalUsers': totalUsers,
        'activeUsers': activeUsers,
        'bannedUsers': bannedUsers,
        'totalRedemptions': totalRedemptions,
        'pendingRedemptions': pendingRedemptions,
        'completedRedemptions': completedRedemptions,
        'totalPoints': totalPoints,
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      debugPrint('Error getting dashboard stats: $e');
      return {};
    }
  }

  // Get admin action logs
  static Future<List<AdminActionLog>> getAdminLogs({int? limit}) async {
    verifyAdminAccess();

    try {
      Query query = _firestore
          .collection('admin_logs')
          .orderBy('timestamp', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => AdminActionLog.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting admin logs: $e');
      return [];
    }
  }

  // LEADERBOARD MANAGEMENT
  static Future<Map<String, dynamic>> getLeaderboard(String type) async {
    verifyAdminAccess();

    final ref = _database.ref().child('leaderboard').child(type);
    final snapshot = await ref.get();

    if (snapshot.exists) {
      return Map<String, dynamic>.from(snapshot.value as Map);
    }
    return {};
  }

  static Future<void> resetLeaderboard(String type) async {
    verifyAdminAccess();

    await _database.ref().child('leaderboard').child(type).remove();
    debugPrint('$type leaderboard reset successfully');
  }

  static Future<void> removeUserFromLeaderboard(
    String type,
    String userId,
  ) async {
    verifyAdminAccess();

    await _database
        .ref()
        .child('leaderboard')
        .child(type)
        .child(userId)
        .remove();
    debugPrint('User removed from $type leaderboard');
  }
}
