import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart' as rtdb;
import 'package:flutter/foundation.dart';
import '../models/admin_models.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class AdminService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final rtdb.FirebaseDatabase _database = rtdb.FirebaseDatabase.instance;

  // Check if current user is admin
  static bool isAdmin() {
    final user = _auth.currentUser;
    return user != null && user.email == AppConstants.adminEmail;
  }

  // Verify admin access
  static void verifyAdminAccess() {
    if (!isAdmin()) {
      throw Exception('Unauthorized: Admin access required');
    }
  }

  // Check if Firebase rules allow admin access
  static Future<bool> hasAdminPermissions() async {
    try {
      if (!isAdmin()) return false;

      // Try a simple read operation to test permissions
      await _firestore.collection('users').limit(1).get();
      return true;
    } catch (e) {
      debugPrint('Admin permissions check failed: $e');
      return false;
    }
  }

  // QUIZ MANAGEMENT
  static Future<List<AdminQuizQuestion>> getQuizQuestions({
    String? category,
    String? difficulty,
  }) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.quizCollection);

    if (category != null && category.isNotEmpty) {
      query = query.where('category', isEqualTo: category);
    }

    if (difficulty != null && difficulty.isNotEmpty) {
      query = query.where('difficulty', isEqualTo: difficulty);
    }

    final snapshot = await query.orderBy('createdAt', descending: true).get();
    return snapshot.docs
        .map((doc) => AdminQuizQuestion.fromFirestore(doc))
        .toList();
  }

  static Future<void> addQuizQuestion(AdminQuizQuestion question) async {
    verifyAdminAccess();

    final questionData = question.toFirestore();
    questionData['createdAt'] = FieldValue.serverTimestamp();

    await _firestore.collection(AppConstants.quizCollection).add(questionData);
    debugPrint('Quiz question added successfully');
  }

  static Future<void> updateQuizQuestion(AdminQuizQuestion question) async {
    verifyAdminAccess();

    final questionData = question.toFirestore();
    questionData['updatedAt'] = FieldValue.serverTimestamp();

    await _firestore
        .collection(AppConstants.quizCollection)
        .doc(question.id)
        .update(questionData);
    debugPrint('Quiz question updated successfully');
  }

  static Future<void> deleteQuizQuestion(String questionId) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.quizCollection)
        .doc(questionId)
        .delete();
    debugPrint('Quiz question deleted successfully');
  }

  // REDEMPTION MANAGEMENT
  static Future<List<AdminRedemptionRequest>> getRedemptionRequests({
    String status = 'all',
  }) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.redemptionsCollection);

    if (status != 'all') {
      query = query.where('status', isEqualTo: status);
    }

    final snapshot = await query.orderBy('requestDate', descending: true).get();
    return snapshot.docs
        .map((doc) => AdminRedemptionRequest.fromFirestore(doc))
        .toList();
  }

  static Future<void> updateRedemptionStatus(
    String requestId,
    String status,
    String? adminNotes,
  ) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.redemptionsCollection)
        .doc(requestId)
        .update({
          'status': status,
          'processedDate': FieldValue.serverTimestamp(),
          'adminNotes': adminNotes,
        });
    debugPrint('Redemption request updated: $status');
  }

  // TIPS MANAGEMENT
  static Future<List<AdminTip>> getTips({String? category}) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.tipsCollection);

    if (category != null && category.isNotEmpty) {
      query = query.where('category', isEqualTo: category);
    }

    final snapshot = await query.orderBy('createdAt', descending: true).get();
    return snapshot.docs.map((doc) => AdminTip.fromFirestore(doc)).toList();
  }

  static Future<void> addTip(AdminTip tip) async {
    verifyAdminAccess();

    final tipData = tip.toFirestore();
    tipData['createdAt'] = FieldValue.serverTimestamp();

    await _firestore.collection(AppConstants.tipsCollection).add(tipData);
    debugPrint('Tip added successfully');
  }

  static Future<void> updateTip(AdminTip tip) async {
    verifyAdminAccess();

    final tipData = tip.toFirestore();
    tipData['updatedAt'] = FieldValue.serverTimestamp();

    await _firestore
        .collection(AppConstants.tipsCollection)
        .doc(tip.id)
        .update(tipData);
    debugPrint('Tip updated successfully');
  }

  static Future<void> deleteTip(String tipId) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.tipsCollection)
        .doc(tipId)
        .delete();
    debugPrint('Tip deleted successfully');
  }

  // USER MANAGEMENT
  static Future<List<AdminUserInfo>> getUsers({
    int limit = 50,
    String? searchEmail,
  }) async {
    verifyAdminAccess();

    Query query = _firestore.collection(AppConstants.usersCollection);

    if (searchEmail != null && searchEmail.isNotEmpty) {
      query = query.where('email', isEqualTo: searchEmail);
    }

    final snapshot =
        await query.orderBy('createdAt', descending: true).limit(limit).get();

    return snapshot.docs
        .map((doc) => AdminUserInfo.fromFirestore(doc))
        .toList();
  }

  static Future<void> banUser(String userId, bool isBanned) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.usersCollection)
        .doc(userId)
        .update({
          'isBanned': isBanned,
          'bannedAt': isBanned ? FieldValue.serverTimestamp() : null,
        });
    debugPrint('User ${isBanned ? 'banned' : 'unbanned'} successfully');
  }

  static Future<void> updateUserPoints(String userId, int points) async {
    verifyAdminAccess();

    await _firestore
        .collection(AppConstants.usersCollection)
        .doc(userId)
        .update({'points': points, 'updatedAt': FieldValue.serverTimestamp()});
    debugPrint('User points updated successfully');
  }

  // ANALYTICS
  static Future<AdminAnalytics> getAnalytics() async {
    verifyAdminAccess();

    try {
      // Get analytics document or create default
      final analyticsDoc =
          await _firestore
              .collection(AppConstants.analyticsCollection)
              .doc('stats')
              .get();

      if (analyticsDoc.exists) {
        return AdminAnalytics.fromFirestore(analyticsDoc);
      } else {
        // Calculate analytics from scratch
        return await _calculateAnalytics();
      }
    } catch (e) {
      debugPrint('Error getting analytics: $e');
      return await _calculateAnalytics();
    }
  }

  static Future<AdminAnalytics> _calculateAnalytics() async {
    final now = DateTime.now();
    final oneDayAgo = now.subtract(const Duration(days: 1));
    final oneWeekAgo = now.subtract(const Duration(days: 7));

    // Get total users
    final usersSnapshot =
        await _firestore.collection(AppConstants.usersCollection).get();
    final totalUsers = usersSnapshot.docs.length;

    // Get daily active users
    final dailyActiveSnapshot =
        await _firestore
            .collection(AppConstants.usersCollection)
            .where('lastLogin', isGreaterThan: Timestamp.fromDate(oneDayAgo))
            .get();
    final dailyActiveUsers = dailyActiveSnapshot.docs.length;

    // Get weekly active users
    final weeklyActiveSnapshot =
        await _firestore
            .collection(AppConstants.usersCollection)
            .where('lastLogin', isGreaterThan: Timestamp.fromDate(oneWeekAgo))
            .get();
    final weeklyActiveUsers = weeklyActiveSnapshot.docs.length;

    // Get redemption stats
    final redemptionsSnapshot =
        await _firestore.collection(AppConstants.redemptionsCollection).get();
    final totalRedemptions = redemptionsSnapshot.docs.length;

    final pendingRedemptions =
        redemptionsSnapshot.docs
            .where(
              (doc) =>
                  (doc.data() as Map<String, dynamic>)['status'] == 'pending',
            )
            .length;

    // Calculate total points
    int totalPoints = 0;
    for (final doc in usersSnapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      totalPoints += (data['points'] as int?) ?? 0;
    }

    final analytics = AdminAnalytics(
      totalUsers: totalUsers,
      dailyActiveUsers: dailyActiveUsers,
      weeklyActiveUsers: weeklyActiveUsers,
      totalSpins: 0, // TODO: Implement spin tracking
      totalQuizzes: 0, // TODO: Implement quiz tracking
      totalRedemptions: totalRedemptions,
      pendingRedemptions: pendingRedemptions,
      totalPoints: totalPoints,
      lastUpdated: now,
    );

    // Save analytics for caching
    await _firestore
        .collection(AppConstants.analyticsCollection)
        .doc('stats')
        .set(analytics.toFirestore());

    return analytics;
  }

  // LEADERBOARD MANAGEMENT
  static Future<Map<String, dynamic>> getLeaderboard(String type) async {
    verifyAdminAccess();

    final ref = _database.ref().child('leaderboard').child(type);
    final snapshot = await ref.get();

    if (snapshot.exists) {
      return Map<String, dynamic>.from(snapshot.value as Map);
    }
    return {};
  }

  static Future<void> resetLeaderboard(String type) async {
    verifyAdminAccess();

    await _database.ref().child('leaderboard').child(type).remove();
    debugPrint('$type leaderboard reset successfully');
  }

  static Future<void> removeUserFromLeaderboard(
    String type,
    String userId,
  ) async {
    verifyAdminAccess();

    await _database
        .ref()
        .child('leaderboard')
        .child(type)
        .child(userId)
        .remove();
    debugPrint('User removed from $type leaderboard');
  }
}
