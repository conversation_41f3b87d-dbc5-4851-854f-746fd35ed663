import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/redemption_model.dart';
import '../utils/constants.dart';
import 'firebase_service.dart';

class RedemptionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Submit withdrawal request
  static Future<String> submitWithdrawalRequest(
    RedemptionRequest request,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      print('Submitting withdrawal request for user: ${user.uid}');

      // Verify user has enough points
      final userDoc =
          await _firestore
              .collection('users') // Use direct collection name
              .doc(user.uid)
              .get();

      if (!userDoc.exists) {
        throw Exception('User profile not found');
      }

      final userData = userDoc.data()!;
      final currentPoints = userData['points'] ?? 0;

      print('User has $currentPoints points, requesting ${request.pointsUsed}');

      if (currentPoints < request.pointsUsed) {
        throw Exception('Insufficient points');
      }

      // Create a simple request data map
      final requestData = {
        'userId': user.uid,
        'userName': request.userName,
        'userEmail': request.userEmail,
        'type': request.type.toString().split('.').last,
        'pointsUsed': request.pointsUsed,
        'amount': request.amount,
        'status': 'pending',
        'requestDate': FieldValue.serverTimestamp(),
        'upiId': request.upiId,
        'gameId': request.gameId,
        'phoneNumber': request.phoneNumber,
      };

      print('Creating redemption request with data: $requestData');

      // Create withdrawal request
      final docRef = await _firestore
          .collection('redemption_requests')
          .add(requestData);

      print('Request created with ID: ${docRef.id}');

      // DO NOT deduct points here - only deduct when admin approves/completes
      print(
        'Request submitted successfully - points will be deducted when approved',
      );

      return docRef.id;
    } catch (e) {
      print('Error submitting withdrawal request: $e');
      throw Exception('Failed to submit withdrawal request: $e');
    }
  }

  // Get user's withdrawal history
  static Future<List<RedemptionRequest>> getUserWithdrawalHistory(
    String userId,
  ) async {
    try {
      final snapshot =
          await _firestore
              .collection('redemption_requests')
              .where('userId', isEqualTo: userId)
              .orderBy('requestDate', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to load withdrawal history: $e');
    }
  }

  // Cancel withdrawal request (only if pending)
  static Future<void> cancelWithdrawalRequest(String requestId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final requestDoc =
          await _firestore
              .collection('redemption_requests')
              .doc(requestId)
              .get();

      if (!requestDoc.exists) {
        throw Exception('Withdrawal request not found');
      }

      final request = RedemptionRequest.fromFirestore(requestDoc);

      // Check if request belongs to user and is pending
      if (request.userId != user.uid) {
        throw Exception('Unauthorized');
      }

      if (request.status != RedemptionStatus.pending) {
        throw Exception('Cannot cancel processed request');
      }

      // Update request status
      await _firestore.collection('redemption_requests').doc(requestId).update({
        'status': RedemptionStatus.cancelled.toString().split('.').last,
        'processedDate': FieldValue.serverTimestamp(),
        'adminNotes': 'Cancelled by user',
      });

      // NO NEED to refund points since they were never deducted
      print(
        'Request cancelled - no points to refund since they were not deducted yet',
      );
    } catch (e) {
      throw Exception('Failed to cancel withdrawal request: $e');
    }
  }

  // Get pending requests for admin
  static Future<List<RedemptionRequest>> getPendingRequests() async {
    try {
      final snapshot =
          await _firestore
              .collection('redemption_requests')
              .where('status', isEqualTo: 'pending')
              .orderBy('requestDate', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to load pending requests: $e');
    }
  }

  // Get all requests for admin
  static Future<List<RedemptionRequest>> getAllRequests({int? limit}) async {
    try {
      Query query = _firestore
          .collection('redemption_requests')
          .orderBy('requestDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to load requests: $e');
    }
  }

  // Update request status (admin only)
  static Future<void> updateRequestStatus({
    required String requestId,
    required RedemptionStatus status,
    String? adminNotes,
    String? transactionId,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get the request details first
      final requestDoc =
          await _firestore
              .collection('redemption_requests')
              .doc(requestId)
              .get();

      if (!requestDoc.exists) {
        throw Exception('Request not found');
      }

      final requestData = requestDoc.data()!;
      final currentStatus = requestData['status'];
      final userId = requestData['userId'];
      final pointsUsed = requestData['pointsUsed'] ?? 0;

      print(
        'Updating request $requestId from $currentStatus to ${status.toString().split('.').last}',
      );

      final updateData = {
        'status': status.toString().split('.').last,
        'processedDate': FieldValue.serverTimestamp(),
        'processedBy': user.email,
      };

      if (adminNotes != null) {
        updateData['adminNotes'] = adminNotes;
      }

      if (transactionId != null) {
        updateData['transactionId'] = transactionId;
      }

      if (status == RedemptionStatus.completed) {
        updateData['completedDate'] = FieldValue.serverTimestamp();
      }

      // Update the request status
      await _firestore
          .collection('redemption_requests')
          .doc(requestId)
          .update(updateData);

      // Handle point deduction based on status change
      if (currentStatus == 'pending') {
        if (status == RedemptionStatus.approved ||
            status == RedemptionStatus.completed) {
          // Deduct points when approved or completed
          await _deductPointsFromUser(userId, pointsUsed);
          print('Points deducted: $pointsUsed from user: $userId');
        }
      }

      // Handle point refund for rejected requests
      if (status == RedemptionStatus.rejected && currentStatus == 'approved') {
        // Refund points if request was approved but then rejected
        await _refundPointsToUser(userId, pointsUsed);
        print('Points refunded: $pointsUsed to user: $userId');
      }
    } catch (e) {
      print('Error updating request status: $e');
      throw Exception('Failed to update request status: $e');
    }
  }

  // Helper method to deduct points from user
  static Future<void> _deductPointsFromUser(String userId, int points) async {
    try {
      // First check if user has enough points
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      final userData = userDoc.data()!;
      final currentPoints = userData['points'] ?? 0;

      if (currentPoints < points) {
        throw Exception(
          'User has insufficient points (has: $currentPoints, needs: $points)',
        );
      }

      // Deduct points
      await _firestore.collection('users').doc(userId).update({
        'points': FieldValue.increment(-points),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      print('Successfully deducted $points points from user $userId');
    } catch (e) {
      print('Error deducting points: $e');
      throw Exception('Failed to deduct points: $e');
    }
  }

  // Helper method to refund points to user
  static Future<void> _refundPointsToUser(String userId, int points) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'points': FieldValue.increment(points),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      print('Successfully refunded $points points to user $userId');
    } catch (e) {
      print('Error refunding points: $e');
      throw Exception('Failed to refund points: $e');
    }
  }

  // Get withdrawal statistics
  static Future<Map<String, dynamic>> getWithdrawalStats() async {
    try {
      final snapshot = await _firestore.collection('redemption_requests').get();

      final requests =
          snapshot.docs
              .map((doc) => RedemptionRequest.fromFirestore(doc))
              .toList();

      final totalRequests = requests.length;
      final pendingRequests =
          requests.where((r) => r.status == RedemptionStatus.pending).length;
      final completedRequests =
          requests.where((r) => r.status == RedemptionStatus.completed).length;
      final rejectedRequests =
          requests.where((r) => r.status == RedemptionStatus.rejected).length;

      final totalPointsWithdrawn = requests
          .where((r) => r.status == RedemptionStatus.completed)
          .fold<int>(0, (sum, r) => sum + r.pointsUsed);

      final totalAmountPaid = requests
          .where((r) => r.status == RedemptionStatus.completed)
          .fold<double>(0, (sum, r) => sum + r.amount);

      return {
        'totalRequests': totalRequests,
        'pendingRequests': pendingRequests,
        'completedRequests': completedRequests,
        'rejectedRequests': rejectedRequests,
        'totalPointsWithdrawn': totalPointsWithdrawn,
        'totalAmountPaid': totalAmountPaid,
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      throw Exception('Failed to load withdrawal stats: $e');
    }
  }

  // Search requests by user email or ID
  static Future<List<RedemptionRequest>> searchRequests(
    String searchTerm,
  ) async {
    try {
      final emailSnapshot =
          await _firestore
              .collection('redemption_requests')
              .where('userEmail', isGreaterThanOrEqualTo: searchTerm)
              .where('userEmail', isLessThanOrEqualTo: searchTerm + '\uf8ff')
              .get();

      final nameSnapshot =
          await _firestore
              .collection('redemption_requests')
              .where('userName', isGreaterThanOrEqualTo: searchTerm)
              .where('userName', isLessThanOrEqualTo: searchTerm + '\uf8ff')
              .get();

      final requests = <RedemptionRequest>[];
      final seenIds = <String>{};

      for (final doc in [...emailSnapshot.docs, ...nameSnapshot.docs]) {
        if (!seenIds.contains(doc.id)) {
          requests.add(RedemptionRequest.fromFirestore(doc));
          seenIds.add(doc.id);
        }
      }

      requests.sort((a, b) => b.requestDate.compareTo(a.requestDate));
      return requests;
    } catch (e) {
      throw Exception('Failed to search requests: $e');
    }
  }

  // Get requests by status
  static Future<List<RedemptionRequest>> getRequestsByStatus(
    RedemptionStatus status,
  ) async {
    try {
      final snapshot =
          await _firestore
              .collection('redemption_requests')
              .where('status', isEqualTo: status.toString().split('.').last)
              .orderBy('requestDate', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to load requests by status: $e');
    }
  }

  // Get requests by type
  static Future<List<RedemptionRequest>> getRequestsByType(
    RedemptionType type,
  ) async {
    try {
      final snapshot =
          await _firestore
              .collection('redemption_requests')
              .where('type', isEqualTo: type.toString().split('.').last)
              .orderBy('requestDate', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to load requests by type: $e');
    }
  }
}
