import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/wallet_model.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class WalletService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get user's wallet
  static Future<WalletModel?> getUserWallet(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.walletCollection)
          .doc(userId)
          .get();
      
      if (doc.exists) {
        return WalletModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user wallet: $e');
      return null;
    }
  }

  // Create or update wallet
  static Future<void> updateWallet(WalletModel wallet) async {
    try {
      await _firestore
          .collection(AppConstants.walletCollection)
          .doc(wallet.userId)
          .set(wallet.toFirestore(), SetOptions(merge: true));
    } catch (e) {
      debugPrint('Error updating wallet: $e');
      throw Exception('Failed to update wallet');
    }
  }

  // Calculate conversion rates
  static int pointsToRupees(int points) {
    return points ~/ AppConstants.pointsToRupeesRate;
  }

  static int pointsToDiamonds(int points) {
    return points ~/ AppConstants.pointsToDiamondsRate;
  }

  static int rupeesToPoints(int rupees) {
    return rupees * AppConstants.pointsToRupeesRate;
  }

  static int diamondsToPoints(int diamonds) {
    return diamonds * AppConstants.pointsToDiamondsRate;
  }

  // Validate redemption request
  static String? validateRedemptionRequest({
    required RedemptionType type,
    required int points,
    required int amount,
    required int userPoints,
  }) {
    // Check if user has enough points
    if (userPoints < points) {
      return 'Insufficient points. You need $points points but only have $userPoints.';
    }

    // Validate minimum amounts
    if (type == RedemptionType.rupees) {
      if (amount < AppConstants.minRupeeRedemption) {
        return 'Minimum rupee redemption is ₹${AppConstants.minRupeeRedemption}';
      }
      if (amount > AppConstants.maxRupeeRedemption) {
        return 'Maximum rupee redemption is ₹${AppConstants.maxRupeeRedemption}';
      }
    } else if (type == RedemptionType.diamonds) {
      if (amount < AppConstants.minDiamondRedemption) {
        return 'Minimum diamond redemption is ${AppConstants.minDiamondRedemption} diamonds';
      }
      if (amount > AppConstants.maxDiamondRedemption) {
        return 'Maximum diamond redemption is ${AppConstants.maxDiamondRedemption} diamonds';
      }
    }

    return null; // Valid request
  }

  // Submit redemption request
  static Future<String> submitRedemptionRequest({
    required UserModel user,
    required RedemptionType type,
    required int amount,
    String? paymentDetails,
    String? gameId,
  }) async {
    try {
      // Calculate points needed
      int pointsNeeded;
      if (type == RedemptionType.rupees) {
        pointsNeeded = rupeesToPoints(amount);
      } else {
        pointsNeeded = diamondsToPoints(amount);
      }

      // Validate request
      final validationError = validateRedemptionRequest(
        type: type,
        points: pointsNeeded,
        amount: amount,
        userPoints: user.points,
      );

      if (validationError != null) {
        throw Exception(validationError);
      }

      // Create redemption request
      final request = RedemptionRequest(
        id: '', // Will be set by Firestore
        userId: user.uid,
        userName: user.name,
        userEmail: user.email,
        type: type,
        pointsUsed: pointsNeeded,
        amount: amount,
        status: RedemptionStatus.pending,
        requestDate: DateTime.now(),
        paymentDetails: paymentDetails,
        gameId: gameId,
      );

      // Add to Firestore
      final docRef = await _firestore
          .collection(AppConstants.redemptionsCollection)
          .add(request.toFirestore());

      return docRef.id;
    } catch (e) {
      debugPrint('Error submitting redemption request: $e');
      throw Exception('Failed to submit redemption request: ${e.toString()}');
    }
  }

  // Get user's redemption history
  static Future<List<RedemptionRequest>> getUserRedemptions(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.redemptionsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('requestDate', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting user redemptions: $e');
      return [];
    }
  }

  // Get all redemption requests (admin only)
  static Future<List<RedemptionRequest>> getAllRedemptions({
    RedemptionStatus? status,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.redemptionsCollection)
          .orderBy('requestDate', descending: true);

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString().split('.').last);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => RedemptionRequest.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting all redemptions: $e');
      return [];
    }
  }

  // Process redemption request (admin only)
  static Future<void> processRedemptionRequest({
    required String requestId,
    required RedemptionStatus newStatus,
    String? adminNotes,
  }) async {
    try {
      final updateData = {
        'status': newStatus.toString().split('.').last,
        'processedDate': Timestamp.fromDate(DateTime.now()),
      };

      if (adminNotes != null) {
        updateData['adminNotes'] = adminNotes;
      }

      await _firestore
          .collection(AppConstants.redemptionsCollection)
          .doc(requestId)
          .update(updateData);
    } catch (e) {
      debugPrint('Error processing redemption request: $e');
      throw Exception('Failed to process redemption request');
    }
  }

  // Get mini games
  static Future<List<MiniGameModel>> getMiniGames() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.miniGamesCollection)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => MiniGameModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting mini games: $e');
      return [];
    }
  }

  // Update mini game (admin only)
  static Future<void> updateMiniGame(MiniGameModel game) async {
    try {
      await _firestore
          .collection(AppConstants.miniGamesCollection)
          .doc(game.id)
          .set(game.toFirestore(), SetOptions(merge: true));
    } catch (e) {
      debugPrint('Error updating mini game: $e');
      throw Exception('Failed to update mini game');
    }
  }

  // Submit support ticket
  static Future<String> submitSupportTicket({
    required UserModel user,
    required String subject,
    required String message,
  }) async {
    try {
      final ticket = SupportTicket(
        id: '',
        userId: user.uid,
        userName: user.name,
        userEmail: user.email,
        subject: subject,
        message: message,
        createdAt: DateTime.now(),
      );

      final docRef = await _firestore
          .collection(AppConstants.supportCollection)
          .add(ticket.toFirestore());

      return docRef.id;
    } catch (e) {
      debugPrint('Error submitting support ticket: $e');
      throw Exception('Failed to submit support ticket');
    }
  }

  // Get support tickets (admin only)
  static Future<List<SupportTicket>> getSupportTickets({
    String? status,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.supportCollection)
          .orderBy('createdAt', descending: true);

      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => SupportTicket.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting support tickets: $e');
      return [];
    }
  }
}
