import 'package:flutter/material.dart';
import 'dart:math';
import 'admob_service.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  // Track navigation count for interstitial ads
  int _navigationCount = 0;
  final int _adFrequency = 3; // Show ad every 3 back navigations

  /// Handle back navigation with potential interstitial ad
  static Future<bool> handleBackNavigation(BuildContext context) async {
    final instance = NavigationService.instance;
    instance._navigationCount++;

    // Show interstitial ad every few navigations
    if (instance._navigationCount >= instance._adFrequency) {
      instance._navigationCount = 0; // Reset counter
      
      // Show interstitial ad
      await _showInterstitialAd(context);
    }

    return true; // Allow navigation
  }

  /// Show interstitial ad with fallback
  static Future<void> _showInterstitialAd(BuildContext context) async {
    try {
      await AdMobService.instance.showInterstitialAd(
        onAdClosed: () {
          debugPrint('Interstitial ad closed');
        },
        onError: (error) {
          debugPrint('Interstitial ad error: $error');
        },
      );
    } catch (e) {
      debugPrint('Failed to show interstitial ad: $e');
    }
  }

  /// Navigate with potential interstitial ad
  static Future<T?> navigateWithAd<T>(
    BuildContext context,
    Widget destination, {
    bool showAd = false,
  }) async {
    if (showAd) {
      await _showInterstitialAd(context);
    }

    return Navigator.of(context).push<T>(
      MaterialPageRoute(builder: (context) => destination),
    );
  }

  /// Pop with potential interstitial ad
  static Future<void> popWithAd(BuildContext context) async {
    final shouldShowAd = await handleBackNavigation(context);
    if (shouldShowAd && context.mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Reset navigation counter (useful for app lifecycle events)
  static void resetCounter() {
    NavigationService.instance._navigationCount = 0;
  }
}

/// Custom WillPopScope wrapper that handles interstitial ads
class AdAwareWillPopScope extends StatelessWidget {
  final Widget child;
  final VoidCallback? onWillPop;

  const AdAwareWillPopScope({
    super.key,
    required this.child,
    this.onWillPop,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        
        // Handle custom onWillPop if provided
        if (onWillPop != null) {
          onWillPop!();
          return;
        }

        // Handle back navigation with ads
        final shouldPop = await NavigationService.handleBackNavigation(context);
        if (shouldPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: child,
    );
  }
}
