import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../utils/constants.dart';

class AdMobService {
  static AdMobService? _instance;
  static AdMobService get instance => _instance ??= AdMobService._();

  AdMobService._();

  RewardedAd? _rewardedAd;
  InterstitialAd? _interstitialAd;
  bool _isRewardedAdReady = false;
  bool _isInterstitialAdReady = false;

  // Initialize AdMob
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
  }

  // Rewarded Ad Methods
  Future<void> loadRewardedAd() async {
    await RewardedAd.load(
      adUnitId: _getRewardedAdUnitId(),
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          _rewardedAd = ad;
          _isRewardedAdReady = true;
          _setRewardedAdCallbacks();
        },
        onAdFailedToLoad: (LoadAdError error) {
          _isRewardedAdReady = false;
          _rewardedAd = null;
          debugPrint('RewardedAd failed to load: $error');
        },
      ),
    );
  }

  void _setRewardedAdCallbacks() {
    _rewardedAd?.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (RewardedAd ad) {
        debugPrint('Rewarded ad showed full screen content');
      },
      onAdDismissedFullScreenContent: (RewardedAd ad) {
        debugPrint('Rewarded ad dismissed full screen content');
        ad.dispose();
        _rewardedAd = null;
        _isRewardedAdReady = false;
        loadRewardedAd(); // Load next ad
      },
      onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
        debugPrint('Rewarded ad failed to show full screen content: $error');
        ad.dispose();
        _rewardedAd = null;
        _isRewardedAdReady = false;
        loadRewardedAd(); // Load next ad
      },
    );
  }

  Future<bool> showRewardedAd({
    required Function(int points) onRewarded,
    Function()? onAdClosed,
    Function(String error)? onError,
  }) async {
    if (!_isRewardedAdReady || _rewardedAd == null) {
      onError?.call('Rewarded ad is not ready');
      return false;
    }

    bool rewardEarned = false;

    await _rewardedAd!.show(
      onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        rewardEarned = true;
        // Calculate bonus points (usually 20-50% of spin reward)
        final bonusPoints =
            (DateTime.now().millisecondsSinceEpoch % 31) + 20; // 20-50 points
        onRewarded(bonusPoints);
      },
    );

    // Wait for ad to close
    await Future.delayed(const Duration(milliseconds: 500));
    onAdClosed?.call();

    return rewardEarned;
  }

  bool get isRewardedAdReady => _isRewardedAdReady;

  // Interstitial Ad Methods
  Future<void> loadInterstitialAd() async {
    await InterstitialAd.load(
      adUnitId: _getInterstitialAdUnitId(),
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          _interstitialAd = ad;
          _isInterstitialAdReady = true;
          _setInterstitialAdCallbacks();
        },
        onAdFailedToLoad: (LoadAdError error) {
          _isInterstitialAdReady = false;
          _interstitialAd = null;
          debugPrint('InterstitialAd failed to load: $error');
        },
      ),
    );
  }

  void _setInterstitialAdCallbacks() {
    _interstitialAd?.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (InterstitialAd ad) {
        debugPrint('Interstitial ad showed full screen content');
      },
      onAdDismissedFullScreenContent: (InterstitialAd ad) {
        debugPrint('Interstitial ad dismissed full screen content');
        ad.dispose();
        _interstitialAd = null;
        _isInterstitialAdReady = false;
        loadInterstitialAd(); // Load next ad
      },
      onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
        debugPrint(
          'Interstitial ad failed to show full screen content: $error',
        );
        ad.dispose();
        _interstitialAd = null;
        _isInterstitialAdReady = false;
        loadInterstitialAd(); // Load next ad
      },
    );
  }

  Future<void> showInterstitialAd({
    Function()? onAdClosed,
    Function(String error)? onError,
  }) async {
    if (!_isInterstitialAdReady || _interstitialAd == null) {
      onError?.call('Interstitial ad is not ready');
      return;
    }

    await _interstitialAd!.show();

    // Wait for ad to close
    await Future.delayed(const Duration(milliseconds: 500));
    onAdClosed?.call();
  }

  bool get isInterstitialAdReady => _isInterstitialAdReady;

  // Banner Ad Creation
  BannerAd createBannerAd() {
    return BannerAd(
      adUnitId: _getBannerAdUnitId(),
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (Ad ad) => debugPrint('Banner ad loaded'),
        onAdFailedToLoad: (Ad ad, LoadAdError error) {
          debugPrint('Banner ad failed to load: $error');
          ad.dispose();
        },
      ),
    );
  }

  // Ad Unit ID Getters
  String _getRewardedAdUnitId() {
    if (Platform.isAndroid) {
      return AppConstants
          .rewardedAdTestId; // Replace with real ID in production
    } else if (Platform.isIOS) {
      return 'ca-app-pub-3940256099942544/1712485313'; // iOS test ID
    }
    return AppConstants.rewardedAdTestId;
  }

  String _getInterstitialAdUnitId() {
    if (Platform.isAndroid) {
      return AppConstants
          .interstitialAdTestId; // Replace with real ID in production
    } else if (Platform.isIOS) {
      return 'ca-app-pub-3940256099942544/4411468910'; // iOS test ID
    }
    return AppConstants.interstitialAdTestId;
  }

  String _getBannerAdUnitId() {
    if (Platform.isAndroid) {
      return 'ca-app-pub-3940256099942544/6300978111'; // Android test ID
    } else if (Platform.isIOS) {
      return 'ca-app-pub-3940256099942544/2934735716'; // iOS test ID
    }
    return 'ca-app-pub-3940256099942544/6300978111';
  }

  // Preload ads
  Future<void> preloadAds() async {
    await Future.wait([loadRewardedAd(), loadInterstitialAd()]);
  }

  // Dispose resources
  void dispose() {
    _rewardedAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd = null;
    _interstitialAd = null;
    _isRewardedAdReady = false;
    _isInterstitialAdReady = false;
  }
}
