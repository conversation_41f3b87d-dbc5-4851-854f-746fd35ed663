import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:uuid/uuid.dart';
import '../models/user_model.dart';
import '../models/quiz_model.dart';
import '../models/reward_model.dart';
import '../utils/constants.dart';

class FirebaseService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();
  static final Uuid _uuid = const Uuid();

  // Authentication Methods
  static User? get currentUser => _auth.currentUser;
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  static Future<UserCredential?> signInWithEmail(
    String email,
    String password,
  ) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  static Future<UserCredential?> signUpWithEmail(
    String email,
    String password,
    String name,
  ) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      if (credential.user != null) {
        await credential.user!.updateDisplayName(name);
        await createUserProfile(credential.user!, name);
      }
      return credential;
    } catch (e) {
      throw Exception('Sign up failed: ${e.toString()}');
    }
  }

  static Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      if (userCredential.user != null) {
        await createUserProfile(
          userCredential.user!,
          googleUser.displayName ?? 'User',
        );
      }
      return userCredential;
    } catch (e) {
      // For ANY Google Sign-In error, check if user is actually signed in
      // This handles all the various Google Sign-In plugin compatibility issues
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        debugPrint(
          'Google Sign-In platform error (known issue): ${e.toString()}',
        );
        debugPrint('User is actually signed in, returning success');
        // Return null and let the auth provider handle the success
        return null;
      }
      throw Exception('Google sign in failed: ${e.toString()}');
    }
  }

  static Future<void> signOut() async {
    await _googleSignIn.signOut();
    await _auth.signOut();
  }

  // User Profile Methods
  static Future<void> createUserProfile(User user, String name) async {
    try {
      final userDoc = _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid);
      final docSnapshot = await userDoc.get();

      if (!docSnapshot.exists) {
        final referralCode = _generateReferralCode();
        final userModel = UserModel(
          uid: user.uid,
          name: name,
          email: user.email ?? '',
          points: 100, // Welcome bonus
          avatar: Avatar(
            hair: 'short_black',
            clothing: 'casual_tshirt',
            background: 'city',
          ),
          lastLogin: DateTime.now(),
          dailyStreak: 1,
          referralCode: referralCode,
          createdAt: DateTime.now(),
          unlockedAvatarItems: ['short_black', 'casual_tshirt', 'city'],
        );

        await userDoc.set(userModel.toFirestore());
        debugPrint('User profile created successfully');
      }
    } catch (e) {
      debugPrint('Failed to create user profile in Firestore: ${e.toString()}');
      // Don't throw error, just log it - we'll use temporary profile
    }
  }

  static Future<UserModel?> getUserProfile(String uid) async {
    try {
      final doc =
          await _firestore
              .collection(AppConstants.usersCollection)
              .doc(uid)
              .get();
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      // If user profile doesn't exist, create it
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await createUserProfile(currentUser, currentUser.displayName ?? 'User');
        final newDoc =
            await _firestore
                .collection(AppConstants.usersCollection)
                .doc(uid)
                .get();
        if (newDoc.exists) {
          return UserModel.fromFirestore(newDoc);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Firestore error: ${e.toString()}');
      // Create a temporary user profile for testing
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        debugPrint('Creating temporary user profile for testing');
        return UserModel(
          uid: uid,
          name:
              currentUser.displayName ??
              currentUser.email?.split('@')[0] ??
              'User',
          email: currentUser.email ?? '',
          points: 100, // Welcome bonus
          avatar: Avatar(
            hair: 'short_black',
            clothing: 'casual_tshirt',
            background: 'city',
          ),
          lastLogin: DateTime.now(),
          dailyStreak: 1,
          referralCode: _generateReferralCode(),
          createdAt: DateTime.now(),
          unlockedAvatarItems: ['short_black', 'casual_tshirt', 'city'],
        );
      }
      return null;
    }
  }

  static Future<void> updateUserProfile(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .update(user.toFirestore());
      debugPrint('User profile updated successfully');
    } catch (e) {
      debugPrint('Failed to update user profile in Firestore: ${e.toString()}');
      // Don't throw error for now - just log it
    }
  }

  static Future<void> updateUserPoints(String uid, int points) async {
    try {
      await _firestore.collection(AppConstants.usersCollection).doc(uid).update(
        {'points': FieldValue.increment(points)},
      );
      debugPrint('User points updated successfully');
    } catch (e) {
      debugPrint('Failed to update points in Firestore: ${e.toString()}');
      // Don't throw error for now - just log it
    }
  }

  // Quiz Methods
  static Future<List<QuizQuestion>> getQuizQuestions(
    int level, {
    int limit = 5,
  }) async {
    try {
      final snapshot =
          await _firestore
              .collection(AppConstants.quizCollection)
              .where('difficulty', isEqualTo: (level % 3) + 1)
              .limit(limit)
              .get();

      return snapshot.docs
          .map((doc) => QuizQuestion.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get quiz questions: ${e.toString()}');
    }
  }

  static Future<void> saveQuizSession(QuizSession session) async {
    try {
      await _firestore
          .collection('quiz_sessions')
          .doc(session.sessionId)
          .set(session.toFirestore());
    } catch (e) {
      throw Exception('Failed to save quiz session: ${e.toString()}');
    }
  }

  // Reward Methods
  static Future<void> addReward(Reward reward) async {
    try {
      await _firestore
          .collection(AppConstants.rewardsCollection)
          .add(reward.toFirestore());
    } catch (e) {
      throw Exception('Failed to add reward: ${e.toString()}');
    }
  }

  static Future<List<Reward>> getUserRewards(
    String uid, {
    int limit = 20,
  }) async {
    try {
      final snapshot =
          await _firestore
              .collection(AppConstants.rewardsCollection)
              .where('userId', isEqualTo: uid)
              .orderBy('timestamp', descending: true)
              .limit(limit)
              .get();

      return snapshot.docs.map((doc) => Reward.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Failed to get user rewards from Firestore: ${e.toString()}');
      // Return empty list for now
      return [];
    }
  }

  // Redemption Methods
  static Future<void> createRedemption(Redemption redemption) async {
    try {
      await _firestore
          .collection(AppConstants.redemptionsCollection)
          .add(redemption.toFirestore());
    } catch (e) {
      throw Exception('Failed to create redemption: ${e.toString()}');
    }
  }

  static Future<List<Redemption>> getUserRedemptions(String uid) async {
    try {
      final snapshot =
          await _firestore
              .collection(AppConstants.redemptionsCollection)
              .where('userId', isEqualTo: uid)
              .orderBy('requestDate', descending: true)
              .get();

      return snapshot.docs.map((doc) => Redemption.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get user redemptions: ${e.toString()}');
    }
  }

  // Leaderboard Methods (using Realtime Database for real-time updates)
  static Future<void> updateLeaderboard(
    String uid,
    String name,
    int points,
    Avatar avatar,
  ) async {
    try {
      final ref = _database.ref().child(AppConstants.leaderboardCollection);

      // Update daily leaderboard
      await ref.child('daily').child(uid).set({
        'name': name,
        'points': points,
        'avatar': avatar.toMap(),
        'timestamp': ServerValue.timestamp,
      });

      // Update weekly leaderboard
      await ref.child('weekly').child(uid).set({
        'name': name,
        'points': points,
        'avatar': avatar.toMap(),
        'timestamp': ServerValue.timestamp,
      });
    } catch (e) {
      throw Exception('Failed to update leaderboard: ${e.toString()}');
    }
  }

  static Stream<List<LeaderboardEntry>> getDailyLeaderboard({int limit = 10}) {
    return _database
        .ref()
        .child(AppConstants.leaderboardCollection)
        .child('daily')
        .orderByChild('points')
        .limitToLast(limit)
        .onValue
        .map((event) {
          final data = event.snapshot.value as Map<dynamic, dynamic>?;
          if (data == null) return <LeaderboardEntry>[];

          final entries = <LeaderboardEntry>[];
          int rank = 1;

          data.entries.toList()
            ..sort(
              (a, b) => (b.value['points'] as int).compareTo(
                a.value['points'] as int,
              ),
            )
            ..forEach((entry) {
              entries.add(
                LeaderboardEntry.fromMap({
                  'uid': entry.key,
                  ...Map<String, dynamic>.from(entry.value),
                }, rank++),
              );
            });

          return entries;
        });
  }

  // Referral Methods
  static Future<bool> validateReferralCode(String code) async {
    try {
      final snapshot =
          await _firestore
              .collection(AppConstants.usersCollection)
              .where('referralCode', isEqualTo: code)
              .limit(1)
              .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  static Future<void> processReferral(
    String referrerCode,
    String newUserUid,
  ) async {
    try {
      // Find referrer
      final referrerSnapshot =
          await _firestore
              .collection(AppConstants.usersCollection)
              .where('referralCode', isEqualTo: referrerCode)
              .limit(1)
              .get();

      if (referrerSnapshot.docs.isNotEmpty) {
        final referrerDoc = referrerSnapshot.docs.first;

        // Add points to referrer
        await referrerDoc.reference.update({
          'points': FieldValue.increment(AppConstants.referralPoints),
        });

        // Add points to new user
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(newUserUid)
            .update({
              'points': FieldValue.increment(AppConstants.referralPoints),
            });

        // Record referral rewards
        final referralReward = Reward(
          id: _uuid.v4(),
          userId: referrerDoc.id,
          type: RewardType.referral,
          points: AppConstants.referralPoints,
          timestamp: DateTime.now(),
          metadata: {'referredUser': newUserUid},
        );

        await addReward(referralReward);
      }
    } catch (e) {
      throw Exception('Failed to process referral: ${e.toString()}');
    }
  }

  // Helper Methods
  static String _generateReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(
      6,
      (index) => chars[(random + index) % chars.length],
    ).join();
  }
}
