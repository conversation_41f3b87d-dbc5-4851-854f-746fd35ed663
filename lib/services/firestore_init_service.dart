import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirestoreInitService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Initialize Firestore collections and settings
  static Future<void> initializeFirestore() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Initialize app settings collection
      await _initializeAppSettings();
      
      // Initialize user-specific collections if needed
      await _initializeUserCollections(user.uid);
      
      print('Firestore initialization completed successfully');
    } catch (e) {
      print('Error initializing Firestore: $e');
    }
  }

  /// Initialize app-wide settings
  static Future<void> _initializeAppSettings() async {
    try {
      final settingsDoc = _firestore.collection('app_settings').doc('conversion_rates');
      final docSnapshot = await settingsDoc.get();
      
      if (!docSnapshot.exists) {
        await settingsDoc.set({
          'rupees_rate': 100, // 100 points = 1 rupee
          'diamonds_rate': 50, // 50 points = 1 diamond
          'min_withdrawal_rupees': 100,
          'min_withdrawal_diamonds': 50,
          'max_withdrawal_rupees': 10000,
          'max_withdrawal_diamonds': 1000,
          'withdrawal_enabled': true,
          'maintenance_mode': false,
          'created_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        });
        print('App settings initialized');
      }
    } catch (e) {
      print('Error initializing app settings: $e');
    }
  }

  /// Initialize user-specific collections
  static Future<void> _initializeUserCollections(String userId) async {
    try {
      // This will be called when user first accesses redemption features
      // Collections will be created automatically when first document is added
      print('User collections ready for: $userId');
    } catch (e) {
      print('Error initializing user collections: $e');
    }
  }

  /// Check if user has admin privileges
  static Future<bool> checkAdminAccess(String email) async {
    try {
      // Check if user is super admin
      if (email == '<EMAIL>') {
        return true;
      }

      // Check if user has admin flag in their profile
      final userDoc = await _firestore.collection('users').doc(_auth.currentUser?.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data();
        return userData?['isAdmin'] == true;
      }

      return false;
    } catch (e) {
      print('Error checking admin access: $e');
      return false;
    }
  }

  /// Create redemption request collection index (call this once from admin)
  static Future<void> createRedemptionIndexes() async {
    try {
      // Note: Firestore indexes are created automatically based on queries
      // But you can create them manually in Firebase Console if needed
      
      // Common query patterns for redemption_requests:
      // 1. where('userId', '==', userId).orderBy('requestDate', 'desc')
      // 2. where('status', '==', status).orderBy('requestDate', 'desc')
      // 3. where('type', '==', type).orderBy('requestDate', 'desc')
      
      print('Redemption indexes will be created automatically on first query');
    } catch (e) {
      print('Error with redemption indexes: $e');
    }
  }

  /// Test Firestore connection and permissions
  static Future<bool> testFirestoreConnection() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        print('No authenticated user for Firestore test');
        return false;
      }

      // Test reading user document
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      print('User document exists: ${userDoc.exists}');

      // Test reading app settings
      final settingsDoc = await _firestore.collection('app_settings').doc('conversion_rates').get();
      print('App settings exist: ${settingsDoc.exists}');

      return true;
    } catch (e) {
      print('Firestore connection test failed: $e');
      return false;
    }
  }

  /// Get conversion rates from Firestore
  static Future<Map<String, dynamic>> getConversionRates() async {
    try {
      final doc = await _firestore.collection('app_settings').doc('conversion_rates').get();
      if (doc.exists) {
        return doc.data() ?? {};
      }
      
      // Return default rates if document doesn't exist
      return {
        'rupees_rate': 100,
        'diamonds_rate': 50,
        'min_withdrawal_rupees': 100,
        'min_withdrawal_diamonds': 50,
      };
    } catch (e) {
      print('Error getting conversion rates: $e');
      return {
        'rupees_rate': 100,
        'diamonds_rate': 50,
        'min_withdrawal_rupees': 100,
        'min_withdrawal_diamonds': 50,
      };
    }
  }

  /// Update conversion rates (admin only)
  static Future<void> updateConversionRates({
    int? rupeesRate,
    int? diamondsRate,
    int? minWithdrawalRupees,
    int? minWithdrawalDiamonds,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null || !await checkAdminAccess(user.email ?? '')) {
        throw Exception('Admin access required');
      }

      final updateData = <String, dynamic>{
        'updated_at': FieldValue.serverTimestamp(),
        'updated_by': user.email,
      };

      if (rupeesRate != null) updateData['rupees_rate'] = rupeesRate;
      if (diamondsRate != null) updateData['diamonds_rate'] = diamondsRate;
      if (minWithdrawalRupees != null) updateData['min_withdrawal_rupees'] = minWithdrawalRupees;
      if (minWithdrawalDiamonds != null) updateData['min_withdrawal_diamonds'] = minWithdrawalDiamonds;

      await _firestore.collection('app_settings').doc('conversion_rates').update(updateData);
      print('Conversion rates updated successfully');
    } catch (e) {
      print('Error updating conversion rates: $e');
      rethrow;
    }
  }
}
