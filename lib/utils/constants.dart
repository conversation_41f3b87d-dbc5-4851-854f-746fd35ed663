class AppConstants {
  // App Info
  static const String appName = 'FireZone';
  static const String appTagline = 'Spin • Quiz • Win Diamonds';
  static const String appVersion = '1.0.0';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String quizCollection = 'quiz';
  static const String rewardsCollection = 'rewards';
  static const String leaderboardCollection = 'leaderboard';
  static const String redemptionsCollection = 'redemptions';
  static const String tipsCollection = 'tips';
  static const String analyticsCollection = 'analytics';
  static const String notificationsCollection = 'notifications';

  // Admin Constants
  static const String adminEmail = '<EMAIL>';
  static const String adminPanelTitle = 'FireZone Admin Panel';

  // Tips Categories
  static const List<String> tipsCategories = [
    'Weapons',
    'Maps',
    'Characters',
    'Gameplay',
    'Strategies',
    'General',
  ];

  // Points System
  static const int minSpinPoints = 10;
  static const int maxSpinPoints = 100;
  static const int minGamePoints = 20;
  static const int maxGamePoints = 50;
  static const int quizPointsPerAnswer = 10;
  static const int dailyLoginBasePoints = 50;
  static const int referralPoints = 100;
  static const int minRedemptionPoints = 500;
  static const int diamondsPerThousandPoints = 100;

  // Timers (in hours)
  static const int spinCooldownHours = 4;
  static const int dailyLoginResetHours = 24;

  // Game Settings
  static const int maxQuizLevels = 50;
  static const int gamesBeforeAd = 3;
  static const int quizLevelsBeforeAd = 3;

  // AdMob Test IDs (Replace with real IDs in production)
  static const String rewardedAdTestId =
      'ca-app-pub-3940256099942544/5224354917';
  static const String interstitialAdTestId =
      'ca-app-pub-3940256099942544/1033173712';

  // Shared Preferences Keys
  static const String keyFirstLaunch = 'first_launch';
  static const String keyLastSpinTime = 'last_spin_time';
  static const String keyLastLoginDate = 'last_login_date';
  static const String keyDailyStreak = 'daily_streak';
  static const String keyGamesPlayed = 'games_played';
  static const String keyQuizLevelsCompleted = 'quiz_levels_completed';

  // Avatar Options
  static const List<String> hairStyles = [
    'short_black',
    'long_brown',
    'spiky_blonde',
    'curly_red',
    'bald',
  ];

  static const List<String> clothingStyles = [
    'casual_tshirt',
    'hoodie',
    'jacket',
    'formal_shirt',
    'tank_top',
  ];

  static const List<String> backgroundStyles = [
    'city',
    'forest',
    'desert',
    'beach',
    'mountains',
  ];

  // Error Messages
  static const String networkError = 'Please check your internet connection';
  static const String authError = 'Authentication failed. Please try again';
  static const String genericError = 'Something went wrong. Please try again';
  static const String insufficientPoints = 'You don\'t have enough points';
  static const String cooldownActive = 'Please wait before spinning again';

  // Success Messages
  static const String pointsEarned = 'Points earned successfully!';
  static const String rewardClaimed = 'Reward claimed successfully!';
  static const String profileUpdated = 'Profile updated successfully!';
  static const String redemptionSubmitted = 'Redemption request submitted!';
}

class GameConstants {
  // Memory Game
  static const int memoryGameGridSize = 4;
  static const int memoryGameTimeLimit = 60; // seconds

  // Tap Target Game
  static const int tapTargetDuration = 30; // seconds
  static const int tapTargetGoal = 20; // targets to hit

  // Quiz Game
  static const int quizTimePerQuestion = 15; // seconds
  static const int quizQuestionsPerLevel = 5;
}
