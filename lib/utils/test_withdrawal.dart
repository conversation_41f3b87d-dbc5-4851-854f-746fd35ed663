import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

class TestWithdrawalHelper {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Test creating a simple withdrawal request
  static Future<void> testSimpleWithdrawal(BuildContext context) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        _showResult(context, 'ERROR: No authenticated user');
        return;
      }

      _showResult(
        context,
        'Testing withdrawal creation...\nUser: ${user.email}',
      );

      // Step 1: Check user document
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) {
        _showResult(context, 'ERROR: User document not found');
        return;
      }

      final userData = userDoc.data()!;
      final currentPoints = userData['points'] ?? 0;

      if (currentPoints < 100) {
        _showResult(
          context,
          'ERROR: Need at least 100 points for test\nCurrent points: $currentPoints',
        );
        return;
      }

      // Step 2: Create simple test request
      final testData = {
        'userId': user.uid,
        'userName': userData['name'] ?? 'Test User',
        'userEmail': user.email ?? '',
        'type': 'rupees',
        'pointsUsed': 100,
        'amount': 1.0,
        'status': 'pending',
        'requestDate': FieldValue.serverTimestamp(),
        'upiId': 'test@upi',
        'phoneNumber': '+************',
      };

      print('Attempting to create document with data: $testData');

      // Step 3: Try to create the document
      final docRef = await _firestore
          .collection('redemption_requests')
          .add(testData);

      print('Document created successfully with ID: ${docRef.id}');

      // Step 4: Points are NOT deducted here - only when admin approves
      _showResult(
        context,
        'SUCCESS! ✅\n\nWithdrawal request created:\nID: ${docRef.id}\nPoints will be deducted when admin approves\n\nYour withdrawal system is working!',
      );
    } catch (e) {
      print('Test withdrawal failed: $e');
      _showResult(
        context,
        'FAILED: $e\n\nThis indicates a Firestore permission issue.\nPlease update your Firestore security rules.',
      );
    }
  }

  /// Test reading withdrawal requests
  static Future<void> testReadWithdrawals(BuildContext context) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        _showResult(context, 'ERROR: No authenticated user');
        return;
      }

      final snapshot =
          await _firestore
              .collection('redemption_requests')
              .where('userId', isEqualTo: user.uid)
              .limit(5)
              .get();

      _showResult(
        context,
        'SUCCESS: Can read withdrawal requests\nFound ${snapshot.docs.length} requests',
      );
    } catch (e) {
      _showResult(context, 'FAILED to read requests: $e');
    }
  }

  /// Clean up test requests
  static Future<void> cleanupTestRequests(BuildContext context) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        _showResult(context, 'ERROR: No authenticated user');
        return;
      }

      final snapshot =
          await _firestore
              .collection('redemption_requests')
              .where('userId', isEqualTo: user.uid)
              .where('upiId', isEqualTo: 'test@upi')
              .get();

      int deletedCount = 0;
      for (final doc in snapshot.docs) {
        await doc.reference.delete();
        deletedCount++;
      }

      // No need to refund points since they were never deducted
      _showResult(
        context,
        'Cleaned up $deletedCount test requests\nNo points to refund (they were never deducted)',
      );
    } catch (e) {
      _showResult(context, 'Error cleaning up: $e');
    }
  }

  static void _showResult(BuildContext context, String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Withdrawal Test'),
            content: SingleChildScrollView(child: Text(message)),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}

/// Test widget for withdrawal functionality
class TestWithdrawalWidget extends StatelessWidget {
  const TestWithdrawalWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Withdrawal System'),
        backgroundColor: Colors.red,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Withdrawal System Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            ElevatedButton(
              onPressed:
                  () => TestWithdrawalHelper.testReadWithdrawals(context),
              child: const Text('Test Read Permissions'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed:
                  () => TestWithdrawalHelper.testSimpleWithdrawal(context),
              child: const Text('Test Create Withdrawal'),
            ),

            const SizedBox(height: 10),

            ElevatedButton(
              onPressed:
                  () => TestWithdrawalHelper.cleanupTestRequests(context),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Cleanup Test Data'),
            ),

            const SizedBox(height: 30),

            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Text(
              '1. Update Firestore rules with the permissive rules\n'
              '2. Test Read Permissions - should work\n'
              '3. Test Create Withdrawal - should create a request\n'
              '4. Check Firebase Console to see the created document\n'
              '5. Cleanup when done testing',
            ),
          ],
        ),
      ),
    );
  }
}
