import 'package:flutter/material.dart';
import '../services/firestore_init_service.dart';
import '../services/redemption_service.dart';

class FirestoreTestWidget extends StatefulWidget {
  const FirestoreTestWidget({super.key});

  @override
  State<FirestoreTestWidget> createState() => _FirestoreTestWidgetState();
}

class _FirestoreTestWidgetState extends State<FirestoreTestWidget> {
  String _testResult = 'Ready to test...';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firestore Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Firestore Connection Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnection,
              child: _isLoading 
                  ? const CircularProgressIndicator()
                  : const Text('Test Firestore Connection'),
            ),
            
            const SizedBox(height: 20),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Test Results:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(_testResult),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Make sure you have updated Firestore security rules\n'
              '2. Ensure you are logged in with a valid user\n'
              '3. Check Firebase Console for any errors\n'
              '4. Verify internet connection',
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _testResult = 'Testing connection...';
    });

    try {
      // Test 1: Basic Firestore connection
      final connectionTest = await FirestoreInitService.testFirestoreConnection();
      if (!connectionTest) {
        setState(() {
          _testResult = 'FAILED: Basic Firestore connection failed';
          _isLoading = false;
        });
        return;
      }

      // Test 2: Get conversion rates
      final rates = await FirestoreInitService.getConversionRates();
      
      // Test 3: Try to get user withdrawal history (this will test permissions)
      try {
        await RedemptionService.getUserWithdrawalHistory('test-user-id');
      } catch (e) {
        // This is expected if user doesn't exist, but should not be permission error
        if (e.toString().contains('PERMISSION_DENIED')) {
          setState(() {
            _testResult = 'FAILED: Permission denied for redemption_requests collection.\n'
                'Please update Firestore security rules.';
            _isLoading = false;
          });
          return;
        }
      }

      setState(() {
        _testResult = 'SUCCESS: All tests passed!\n\n'
            'Connection: ✅\n'
            'Conversion rates: ✅ (Rupees: ${rates['rupees_rate']}, Diamonds: ${rates['diamonds_rate']})\n'
            'Permissions: ✅\n\n'
            'Your redemption system is ready to use!';
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _testResult = 'ERROR: $e\n\n'
            'Common solutions:\n'
            '• Update Firestore security rules\n'
            '• Check internet connection\n'
            '• Verify Firebase project configuration\n'
            '• Ensure user is authenticated';
        _isLoading = false;
      });
    }
  }
}

// Helper function to show test widget
void showFirestoreTest(BuildContext context) {
  Navigator.push(
    context,
    MaterialPageRoute(builder: (_) => const FirestoreTestWidget()),
  );
}
