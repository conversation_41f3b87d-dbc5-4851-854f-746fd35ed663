rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - allow authenticated users to read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Allow admin to read all users
      allow read, write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    // Redemption requests - VERY PERMISSIVE RULES FOR TESTING
    match /redemption_requests/{requestId} {
      // Allow any authenticated user to read/write redemption requests
      allow read, write: if request.auth != null;
    }
    
    // Rewards collection
    match /rewards/{rewardId} {
      allow read, write: if request.auth != null;
    }
    
    // Leaderboard collection
    match /leaderboard/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // App settings collection
    match /app_settings/{settingId} {
      allow read, write: if request.auth != null;
    }
    
    // Admin logs collection
    match /admin_logs/{logId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow all authenticated users to read/write any document (TEMPORARY)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
