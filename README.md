# FireZone – Spin • Quiz • Win Diamonds

A Flutter-based Android app for Free Fire fans to earn points and redeem Free Fire diamonds through various engaging activities.

## Features

- **Spin Wheel**: Spin every 4 hours to earn 10-100 points randomly
- **Mini Games**: Simple games like memory match and tap-the-target (20-50 points per game)
- **Quiz**: Free Fire-themed questions (10 points per correct answer)
- **Daily Login Rewards**: Earn points daily with increasing streak bonuses
- **Avatar Builder**: Customize avatars with unlockable styles
- **Leaderboard**: Daily and weekly rankings
- **Secret Chest**: Watch ads to get random rewards
- **Tips & Tricks**: Free Fire gameplay advice
- **Referral System**: Invite friends and earn 100 points per referral
- **Points Redemption**: Exchange points for Free Fire diamonds

## Tech Stack

- **Flutter**: Latest stable version
- **State Management**: Provider
- **Backend**: Firebase (Auth, Firestore, Realtime Database)
- **Monetization**: Google AdMob
- **UI**: Custom Free Fire-inspired theme with animations

## Setup Instructions

### 1. Prerequisites

- Flutter SDK (latest stable)
- Android Studio / VS Code
- Firebase account
- AdMob account

### 2. Clone and Install

```bash
git clone <repository-url>
cd FireZone
flutter pub get
```

### 3. Firebase Setup

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com)
2. Enable Authentication (Email/Password and Google Sign-In)
3. Create Firestore database
4. Create Realtime Database
5. Download `google-services.json` and place it in `android/app/`
6. Update `lib/firebase_options.dart` with your Firebase configuration

### 4. AdMob Setup

1. Create an AdMob account and app
2. Get your Ad Unit IDs
3. Update `lib/utils/constants.dart` with your real Ad Unit IDs
4. Add AdMob App ID to `android/app/src/main/AndroidManifest.xml`

### 5. Android Configuration

Add to `android/app/src/main/AndroidManifest.xml`:

```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-xxxxxxxxxxxxxxxx~yyyyyyyyyy"/>
```

### 6. Run the App

```bash
flutter run
```

## Project Structure

```
lib/
├── models/          # Data models
├── services/        # Firebase and AdMob services
├── providers/       # State management
├── screens/         # App screens
├── widgets/         # Reusable widgets
├── utils/           # Constants and themes
└── main.dart        # App entry point

assets/
├── images/          # App images
├── icons/           # App icons
├── animations/      # Lottie animations
└── data/            # JSON data files
```

## Key Features Implementation

### Points System
- Spin Wheel: 10-100 points (4-hour cooldown)
- Mini Games: 20-50 points per completion
- Quiz: 10 points per correct answer
- Daily Login: 50+ points (increases with streak)
- Referral: 100 points per successful referral

### Monetization
- Rewarded video ads after spins
- Interstitial ads after games/quiz
- Optional ad viewing for bonus rewards

### Security
- Firebase security rules
- Input validation
- Secure data storage

## Development Notes

- Uses Provider for state management
- Custom theme with Free Fire-inspired colors
- Responsive design for all screen sizes
- Error handling with user-friendly messages
- Loading indicators for better UX

## Testing

Run tests with:
```bash
flutter test
```

## Building for Release

```bash
flutter build apk --release
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is for educational purposes. Not affiliated with Garena or Free Fire.

## Disclaimer

This app is not officially affiliated with Garena or Free Fire. All trademarks belong to their respective owners.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
